/**
 * Navigation Context
 * Provides navigation state management and utilities
 * Handles navigation state refresh and coordination between nested navigators
 */

import React, { createContext, useContext, useCallback, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface NavigationContextType {
  refreshNavigationState: () => Promise<void>;
  triggerAppStateRefresh: () => void;
}

// Create navigation context
const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export interface NavigationProviderProps {
  children: ReactNode;
  onStateRefresh?: () => Promise<void>;
}

/**
 * NavigationProvider component
 * Provides navigation utilities to all child components
 */
export const NavigationProvider: React.FC<NavigationProviderProps> = ({ 
  children, 
  onStateRefresh 
}) => {
  /**
   * Refresh navigation state by re-checking AsyncStorage values
   * This can be called from nested navigators to trigger parent navigation updates
   */
  const refreshNavigationState = useCallback(async () => {
    try {
      console.log('[NavigationContext] Refreshing navigation state...');

      if (onStateRefresh) {
        console.log('[NavigationContext] Calling onStateRefresh handler...');
        await onStateRefresh();
        console.log('[NavigationContext] Navigation state refresh completed');
      } else {
        // Default behavior: just log that refresh was requested
        console.log('[NavigationContext] Navigation state refresh requested but no handler provided');
      }
    } catch (error) {
      console.error('[NavigationContext] Error refreshing navigation state:', error);
    }
  }, [onStateRefresh]);

  /**
   * Trigger app state refresh
   * This is a synchronous version that can be used when async is not needed
   */
  const triggerAppStateRefresh = useCallback(() => {
    console.log('[NavigationContext] Triggering app state refresh...');
    
    // Use setTimeout to ensure this runs after current execution cycle
    setTimeout(() => {
      refreshNavigationState();
    }, 100);
  }, [refreshNavigationState]);

  const contextValue: NavigationContextType = {
    refreshNavigationState,
    triggerAppStateRefresh,
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
};

/**
 * useNavigation hook
 * Provides access to navigation utilities
 */
export const useNavigationContext = (): NavigationContextType => {
  const context = useContext(NavigationContext);
  
  if (!context) {
    // Provide fallback functions if context is not available
    console.warn('[useNavigationContext] Navigation context not found, using fallback functions');
    
    return {
      refreshNavigationState: async () => {
        console.warn('[useNavigationContext] refreshNavigationState called but no context available');
      },
      triggerAppStateRefresh: () => {
        console.warn('[useNavigationContext] triggerAppStateRefresh called but no context available');
      },
    };
  }

  return context;
};

/**
 * Navigation utilities for common operations
 */
export const navigationUtils = {
  /**
   * Check if user is authenticated
   */
  checkAuthentication: async (): Promise<boolean> => {
    try {
      const accessToken = await AsyncStorage.getItem('access_token');
      const user = await AsyncStorage.getItem('user');
      return !!(accessToken && user);
    } catch (error) {
      console.error('[navigationUtils] Error checking authentication:', error);
      return false;
    }
  },

  /**
   * Get current app navigation state
   */
  getAppNavigationState: async () => {
    try {
      const isAuthenticated = await navigationUtils.checkAuthentication();

      return {
        isAuthenticated,
        shouldShowMain: isAuthenticated,
        shouldShowAuth: !isAuthenticated,
      };
    } catch (error) {
      console.error('[navigationUtils] Error getting app navigation state:', error);
      return {
        isAuthenticated: false,
        shouldShowMain: false,
        shouldShowAuth: true,
      };
    }
  },

  /**
   * Clear all navigation-related storage
   */
  clearNavigationState: async (): Promise<void> => {
    try {
      await Promise.all([
        AsyncStorage.removeItem('access_token'),
        AsyncStorage.removeItem('user'),
      ]);
      console.log('[navigationUtils] Navigation state cleared');
    } catch (error) {
      console.error('[navigationUtils] Error clearing navigation state:', error);
    }
  },
};

export default NavigationProvider;
