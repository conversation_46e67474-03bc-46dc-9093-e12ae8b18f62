/**
 * Profile API Service
 * Handles all profile-related API calls
 */

import { apiClient } from './client';

// Types for API requests and responses
export interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone?: string;
  role: 'customer' | 'service_provider' | 'admin';
  avatar?: string;
  date_of_birth?: string;
  bio?: string;
  account_status: string;
  is_verified: boolean;
  email_verified_at?: string;
  phone_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  // Location Information
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  full_address?: string; // computed
  has_location?: boolean; // computed

  // Business Information (for providers)
  business_name?: string;
  business_description?: string;
  years_of_experience?: number;
  website?: string;
  instagram?: string;
  facebook?: string;

  // Preferences
  search_radius?: number;
  auto_accept_bookings?: boolean;
  show_phone_publicly?: boolean;
  show_email_publicly?: boolean;
  allow_reviews?: boolean;

  // Timestamps
  created_at?: string;
  updated_at?: string;
}

export interface ProfileUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone?: string;
  bio?: string;
  date_of_birth?: string;
}

export interface ProfileDetailsUpdateRequest {
  // Location Information
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  latitude?: number;
  longitude?: number;

  // Business Information (for providers)
  business_name?: string;
  business_description?: string;
  years_of_experience?: number;
  website?: string;
  instagram?: string;
  facebook?: string;

  // Preferences
  search_radius?: number;
  auto_accept_bookings?: boolean;
  show_phone_publicly?: boolean;
  show_email_publicly?: boolean;
  allow_reviews?: boolean;
}

export interface AvatarUploadResponse {
  avatar: string;
}

// React Native compatible image type
export interface ImageAsset {
  uri: string;
  type: string;
  name: string;
}

// API Error types
export interface APIError {
  message: string;
  status?: number;
  field_errors?: Record<string, string[]>;
}

// Validation result type
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Enhanced user data with computed properties
export interface EnhancedUser extends User {
  displayName: string;
  hasAvatar: boolean;
}

/**
 * Profile API Service
 * Provides methods for profile management operations
 */
export const profileAPI = {
  /**
   * Get user profile information
   * @returns Promise<User> User profile data
   */
  async getProfile(): Promise<User> {
    try {
      const response = await apiClient.get('/auth/profile/');
      return response.data;
    } catch (error) {
      console.error('Get profile error:', error);
      throw this.handleError(error);
    }
  },

  /**
   * Update user profile information
   * @param data Profile update data
   * @returns Promise<User> Updated user profile data
   */
  async updateProfile(data: ProfileUpdateRequest): Promise<User> {
    try {
      const response = await apiClient.patch('/auth/profile/update/', data);
      return response.data;
    } catch (error) {
      console.error('Update profile error:', error);
      throw this.handleError(error);
    }
  },

  /**
   * Get extended user profile details
   * Uses the backend UserProfile endpoint for extended information
   * @returns Promise<UserProfile> Extended profile data
   */
  async getProfileDetails(): Promise<UserProfile | null> {
    try {
      // Use the backend UserProfile endpoint that exists
      const response = await apiClient.get('/auth/profile/details/');
      return response.data;
    } catch (error: any) {
      // If endpoint doesn't exist (404), return null gracefully
      if (error.response?.status === 404) {
        console.warn('getProfileDetails: Profile details endpoint not found, returning null');
        return null;
      }
      console.error('Get profile details error:', error);
      throw error;
    }
  },

  /**
   * Update extended user profile details
   * Uses the backend UserProfile update endpoint for extended information
   * @param data Profile details update data
   * @returns Promise<UserProfile> Updated profile details
   */
  async updateProfileDetails(data: ProfileDetailsUpdateRequest): Promise<UserProfile | null> {
    try {
      // Use the backend UserProfile update endpoint that exists
      const response = await apiClient.patch('/auth/profile/details/', data);
      return response.data;
    } catch (error: any) {
      // If endpoint doesn't exist (404), fall back to main profile endpoint
      if (error.response?.status === 404) {
        console.warn('updateProfileDetails: Profile details endpoint not found, using main profile endpoint');

        // Map some fields to the main profile endpoint
        const basicFields: ProfileUpdateRequest = {};
        if (data.business_name) basicFields.first_name = data.business_name; // Temporary mapping

        if (Object.keys(basicFields).length > 0) {
          await this.updateProfile(basicFields);
        }

        return null;
      }
      console.error('Update profile details error:', error);
      throw error;
    }
  },

  /**
   * Upload user avatar image
   * Uses the profile update endpoint with multipart/form-data
   * @param imageAsset React Native image asset
   * @param onProgress Optional progress callback
   * @returns Promise<string> Avatar URL
   */
  async uploadAvatar(
    imageAsset: ImageAsset,
    onProgress?: (progress: number) => void
  ): Promise<string> {
    try {
      // Validate image asset
      if (!imageAsset.uri) {
        throw new Error('Invalid image: URI is required');
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(imageAsset.type)) {
        throw new Error('Invalid file type. Please select a JPEG, PNG, or WebP image.');
      }

      const formData = new FormData();
      formData.append('avatar', {
        uri: imageAsset.uri,
        type: imageAsset.type,
        name: imageAsset.name || 'avatar.jpg',
      } as any);

      // Use the profile update endpoint for avatar upload with progress tracking
      const response = await apiClient.patch('/auth/profile/update/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(progress);
          }
        },
      });

      // Validate response
      if (!response.data) {
        throw new Error('Invalid response from server');
      }

      // Return the avatar URL from response or fallback to original URI
      return response.data.avatar || imageAsset.uri;
    } catch (error: any) {
      console.error('Upload avatar error:', error);

      // Enhanced error handling for avatar upload
      if (error.message?.includes('file too large')) {
        throw new Error('Image file is too large. Please select an image smaller than 5MB.');
      }

      if (error.message?.includes('network')) {
        throw new Error('Network error. Please check your connection and try again.');
      }

      throw this.handleError(error);
    }
  },

  /**
   * Delete user avatar image
   * Uses the profile update endpoint to clear the avatar
   * @returns Promise<void>
   */
  async deleteAvatar(): Promise<void> {
    try {
      // Use the profile update endpoint to clear avatar
      await apiClient.patch('/auth/profile/update/', { avatar: null });
    } catch (error) {
      console.error('Delete avatar error:', error);
      throw this.handleError(error);
    }
  },



  /**
   * Enhanced error handler for profile API calls
   * @param error API error object
   * @returns Formatted error object
   */
  handleError(error: any): APIError {
    if (error.response) {
      return {
        message: error.response.data?.detail || error.response.data?.message || 'An error occurred',
        status: error.response.status,
        field_errors: error.response.data?.errors || error.response.data,
      };
    } else if (error.request) {
      return {
        message: 'Network error - please check your connection',
        status: 0,
      };
    } else {
      return {
        message: error.message || 'An unexpected error occurred',
      };
    }
  },

  /**
   * Transform user data for display purposes
   * @param user User data from API
   * @returns Transformed user data
   */
  transformUserData(user: User): User & { displayName: string; hasAvatar: boolean } {
    return {
      ...user,
      displayName: user.full_name || `${user.first_name} ${user.last_name}`.trim() || user.username,
      hasAvatar: Boolean(user.avatar),
    };
  },

  /**
   * Check if profile is complete
   * @param user User profile data
   * @returns Boolean indicating if profile is complete
   */
  isProfileComplete(user: User): boolean {
    const requiredFields = ['first_name', 'last_name', 'email'];
    return requiredFields.every(field => Boolean(user[field as keyof User]));
  },

  /**
   * Validate profile data before submission
   * @param data Profile data to validate
   * @returns Validation result with errors
   */
  validateProfileData(data: ProfileUpdateRequest): ValidationResult {
    const errors: string[] = [];

    // Validate phone number format
    if (data.phone && !data.phone.startsWith('+')) {
      errors.push('Phone number must include country code (e.g., +1234567890)');
    }

    // Validate bio length
    if (data.bio && data.bio.length > 500) {
      errors.push('Bio cannot exceed 500 characters');
    }

    // Validate date of birth format
    if (data.date_of_birth && !/^\d{4}-\d{2}-\d{2}$/.test(data.date_of_birth)) {
      errors.push('Date of birth must be in YYYY-MM-DD format');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Enhanced validation for business profile data
   * @param data Business profile data to validate
   * @returns Validation result with errors
   */
  validateBusinessProfileData(data: Partial<UserProfile>): ValidationResult {
    const errors: string[] = [];

    // Validate website URL format
    if (data.website && !data.website.match(/^https?:\/\/.+/)) {
      errors.push('Website must be a valid URL starting with http:// or https://');
    }

    // Validate years of experience range
    if (data.years_of_experience !== undefined) {
      if (data.years_of_experience < 0 || data.years_of_experience > 50) {
        errors.push('Years of experience must be between 0 and 50');
      }
    }

    // Validate social media URLs
    if (data.instagram && !data.instagram.match(/^https?:\/\/(www\.)?instagram\.com\/.+/)) {
      errors.push('Instagram must be a valid Instagram URL');
    }

    if (data.facebook && !data.facebook.match(/^https?:\/\/(www\.)?facebook\.com\/.+/)) {
      errors.push('Facebook must be a valid Facebook URL');
    }

    // Validate business description length
    if (data.business_description && data.business_description.length > 1000) {
      errors.push('Business description cannot exceed 1000 characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate location data
   * @param data Location data to validate
   * @returns Validation result with errors
   */
  validateLocationData(data: Partial<UserProfile>): ValidationResult {
    const errors: string[] = [];

    // Validate latitude range
    if (data.latitude !== undefined && (data.latitude < -90 || data.latitude > 90)) {
      errors.push('Latitude must be between -90 and 90');
    }

    // Validate longitude range
    if (data.longitude !== undefined && (data.longitude < -180 || data.longitude > 180)) {
      errors.push('Longitude must be between -180 and 180');
    }

    // Validate search radius
    if (data.search_radius !== undefined && (data.search_radius < 1 || data.search_radius > 100)) {
      errors.push('Search radius must be between 1 and 100 miles');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Comprehensive validation for all profile data
   * @param userData User profile data
   * @param profileData Extended profile data
   * @returns Validation result with errors
   */
  validateCompleteProfile(userData: ProfileUpdateRequest, profileData?: Partial<UserProfile>): ValidationResult {
    const userValidation = this.validateProfileData(userData);
    const businessValidation = profileData ? this.validateBusinessProfileData(profileData) : { isValid: true, errors: [] };
    const locationValidation = profileData ? this.validateLocationData(profileData) : { isValid: true, errors: [] };

    const allErrors = [
      ...userValidation.errors,
      ...businessValidation.errors,
      ...locationValidation.errors,
    ];

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
    };
  },

  /**
   * Calculate profile completion percentage
   * @param user User profile data
   * @param profile Extended profile data
   * @returns Profile completion percentage (0-100)
   */
  calculateProfileCompletion(user: User, profile?: UserProfile | null): number {
    const requiredFields = [
      'first_name', 'last_name', 'email', 'phone', 'bio', 'date_of_birth'
    ];

    const optionalFields = [
      'address', 'city', 'state', 'zip_code', 'country'
    ];

    const businessFields = user.role === 'service_provider' ? [
      'business_name', 'business_description', 'years_of_experience'
    ] : [];

    const allFields = [...requiredFields, ...optionalFields, ...businessFields];
    let completedFields = 0;

    // Check user fields
    requiredFields.forEach(field => {
      if (user[field as keyof User]) completedFields++;
    });

    // Check profile fields
    if (profile) {
      optionalFields.forEach(field => {
        if (profile[field as keyof UserProfile]) completedFields++;
      });

      businessFields.forEach(field => {
        if (profile[field as keyof UserProfile]) completedFields++;
      });
    }

    return Math.round((completedFields / allFields.length) * 100);
  },

  /**
   * Format user display name with fallbacks
   * @param user User data
   * @returns Formatted display name
   */
  formatDisplayName(user: User): string {
    if (user.full_name?.trim()) {
      return user.full_name.trim();
    }

    const firstName = user.first_name?.trim();
    const lastName = user.last_name?.trim();

    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }

    if (firstName) {
      return firstName;
    }

    if (lastName) {
      return lastName;
    }

    return user.username || user.email || 'Unknown User';
  },

  /**
   * Format user location string
   * @param profile User profile data
   * @returns Formatted location string
   */
  formatLocation(profile: UserProfile): string {
    const parts = [];

    if (profile.city) parts.push(profile.city);
    if (profile.state) parts.push(profile.state);
    if (profile.country && profile.country !== 'USA') parts.push(profile.country);

    return parts.join(', ') || 'Location not provided';
  },

  /**
   * Check if user has business profile
   * @param user User data
   * @param profile Extended profile data
   * @returns Boolean indicating if user has business information
   */
  hasBusinessProfile(user: User, profile?: UserProfile | null): boolean {
    if (user.role !== 'service_provider') return false;

    return Boolean(
      profile?.business_name ||
      profile?.business_description ||
      profile?.years_of_experience
    );
  },

  /**
   * Get user initials for avatar placeholder
   * @param user User data
   * @returns User initials (max 2 characters)
   */
  getUserInitials(user: User): string {
    const displayName = this.formatDisplayName(user);
    const words = displayName.split(' ').filter(word => word.length > 0);

    if (words.length >= 2) {
      return `${words[0][0]}${words[1][0]}`.toUpperCase();
    }

    if (words.length === 1) {
      return words[0].substring(0, 2).toUpperCase();
    }

    return 'U';
  },

  /**
   * Check if profile data has changed
   * @param original Original profile data
   * @param updated Updated profile data
   * @returns Boolean indicating if data has changed
   */
  hasProfileChanged(original: any, updated: any): boolean {
    const keys = Object.keys(updated);

    return keys.some(key => {
      const originalValue = original[key];
      const updatedValue = updated[key];

      // Handle null/undefined comparison
      if (originalValue == null && updatedValue == null) return false;
      if (originalValue == null || updatedValue == null) return true;

      // Handle string comparison (trim whitespace)
      if (typeof originalValue === 'string' && typeof updatedValue === 'string') {
        return originalValue.trim() !== updatedValue.trim();
      }

      return originalValue !== updatedValue;
    });
  },
};

export default profileAPI;
