{"authentication/tests.py": true, "debug_test.py::test_registration": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_blocked_in_production": true, "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_account_lockout_after_failed_attempts": true, "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_user_cannot_login_with_invalid_credentials": true, "authentication/test_acceptance.py::TestCase": true, "authentication/test_acceptance.py::TransactionTestCase": true, "authentication/test_acceptance.py::APITestCase": true, "authentication/test_acceptance.py::UserRegistrationAcceptanceTests": true, "authentication/test_acceptance.py::UserLoginAcceptanceTests": true, "authentication/test_acceptance.py::EmailVerificationAcceptanceTests": true, "authentication/test_acceptance.py::SocialAuthenticationAcceptanceTests": true, "authentication/test_login_fix.py::TestCase": true, "authentication/test_login_fix.py::APITestCase": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests": true, "authentication/test_login_fix.py::LoginIntegrationTests": true, "authentication/test_urls.py::TestCase": true, "authentication/test_urls.py::APITestCase": true, "authentication/test_urls.py::AuthenticationURLTests": true, "authentication/test_urls.py::AuthenticationURLAccessTests": true, "authentication/tests/test_login_authentication.py::TestCase": true, "authentication/tests/test_login_authentication.py::APITestCase": true, "authentication/tests/test_login_authentication.py::LoginAuthenticationViewTests": true, "authentication/tests/test_login_authentication.py::AccountLockoutTests": true, "authentication/tests/test_login_authentication.py::LoginSerializerTests": true, "authentication/tests/test_login_authentication.py::JWTTokenGenerationTests": true, "authentication/tests/test_login_authentication.py::RateLimitingTests": true, "authentication/tests/test_login_authentication.py::ErrorResponseConsistencyTests": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_test_accounts_force": true, "catalog/tests/test_account_management.py::TestCase": true, "catalog/tests/test_account_management.py::TestAccountSecurity": true, "catalog/tests/test_account_management.py::TestAccountManager": true, "catalog/tests/test_account_management.py::TestAccountCreationTest": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest": true, "catalog/tests/test_account_management.py::TestAccountSecurityTest": true, "catalog/tests/test_account_management.py::SampleDataGenerationTest": true, "catalog/tests/test_account_management.py::DataVerificationTest": true, "catalog/tests/test_account_management.py::SecurityAuditTest": true, "catalog/tests/test_provider_service_api_integration.py::TestCase": true, "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_account_lockout_after_failed_attempts": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_case_insensitive_email_login": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_invalid_email_login_failure": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_invalid_password_login_failure": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_provider_user_login": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_response_format_consistency": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_successful_login_resets_failed_attempts": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_valid_login_success": true, "authentication/tests/test_login_authentication.py::LoginAuthenticationViewTests::test_invalid_credentials_returns_400_status": true, "authentication/tests/test_login_authentication.py::LoginAuthenticationViewTests::test_nonexistent_user_returns_400_status": true, "authentication/tests/test_login_authentication.py::AccountLockoutTests::test_account_lockout_after_failed_attempts": true, "authentication/tests/test_login_authentication.py::AccountLockoutTests::test_locked_account_returns_423_status": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_all_accounts_database_consistency": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_email_not_verified_accounts": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_invalid_credentials_accounts": true, "test_consolidated_accounts.py::TestAccountPasswordValidationTest::test_password_strength_requirements": true, "test_consolidated_accounts.py::TestAccountAPIEndpointsTest::test_login_endpoint_with_all_accounts": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_database_settings_configuration": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_django_database_connection": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_postgresql_connection_available": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_authentication_tables_exist": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_catalog_tables_exist": true, "test_postgresql_config.py::PostgreSQLDataIntegrityTest::test_foreign_key_relationships": true, "test_postgresql_config.py::PostgreSQLPerformanceTest::test_database_indexes_exist": true, "test_postgresql_config.py::PostgreSQLEnvironmentTest::test_postgresql_version_compatibility": true, "authentication/test_acceptance.py": true}