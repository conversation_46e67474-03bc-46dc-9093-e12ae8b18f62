/**
 * AvatarUpload Component Tests
 * Comprehensive test suite for avatar upload modal component
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { AvatarUpload } from '../../../components/profile/AvatarUpload';

// Mock Expo Image Picker
jest.mock('expo-image-picker', () => ({
  requestCameraPermissionsAsync: jest.fn(),
  requestMediaLibraryPermissionsAsync: jest.fn(),
  launchCameraAsync: jest.fn(),
  launchImageLibraryAsync: jest.fn(),
  MediaTypeOptions: {
    Images: 'Images',
  },
}));

// Mock Expo Image Manipulator
jest.mock('expo-image-manipulator', () => ({
  manipulateAsync: jest.fn(),
  SaveFormat: {
    JPEG: 'jpeg',
    PNG: 'png',
  },
}));

// Mock UI components
jest.mock('../../../components/ui/Button', () => ({
  PrimaryButton: ({ children, onPress, testID, ...props }: any) => (
    <button testID={testID} onPress={onPress} data-variant="primary" {...props}>
      {children}
    </button>
  ),
  SecondaryButton: ({ children, onPress, testID, ...props }: any) => (
    <button testID={testID} onPress={onPress} data-variant="secondary" {...props}>
      {children}
    </button>
  ),
  TertiaryButton: ({ children, onPress, testID, ...props }: any) => (
    <button testID={testID} onPress={onPress} data-variant="tertiary" {...props}>
      {children}
    </button>
  ),
}));

// Mock Typography components
jest.mock('../../../components/ui/Typography', () => ({
  Headline2: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
  BodyText: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  CaptionText: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  ButtonText: ({ children, ...props }: any) => <span {...props}>{children}</span>,
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('AvatarUpload', () => {
  const mockOnUpload = jest.fn();
  const mockOnClose = jest.fn();
  const mockOnRemove = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Modal Display', () => {
    it('should display modal when visible', () => {
      render(
        <AvatarUpload
          isVisible={true}
          onUpload={mockOnUpload}
          onClose={mockOnClose}
          onRemove={mockOnRemove}
        />
      );

      expect(screen.getByTestId('avatar-upload-modal')).toBeTruthy();
    });

    it('should not display modal when not visible', () => {
      render(
        <AvatarUpload
          isVisible={false}
          onUpload={mockOnUpload}
          onClose={mockOnClose}
          onRemove={mockOnRemove}
        />
      );

      expect(screen.queryByTestId('avatar-upload-modal')).toBeFalsy();
    });

    it('should display all action options', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByTestId('camera-button')).toBeTruthy();
      expect(screen.getByTestId('library-button')).toBeTruthy();
      expect(screen.getByTestId('delete-button')).toBeTruthy();
      expect(screen.getByTestId('cancel-button')).toBeTruthy();
    });

    it('should hide delete button when no current avatar', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
          hasCurrentAvatar={false}
        />
      );

      expect(screen.getByTestId('camera-button')).toBeTruthy();
      expect(screen.getByTestId('library-button')).toBeTruthy();
      expect(screen.queryByTestId('delete-button')).toBeFalsy();
      expect(screen.getByTestId('cancel-button')).toBeTruthy();
    });
  });

  describe('Camera Functionality', () => {
    it('should handle camera button press', async () => {
      const mockLaunchCamera = require('react-native-image-picker').launchCamera;
      mockLaunchCamera.mockImplementation((options, callback) => {
        callback({
          didCancel: false,
          assets: [{ uri: 'camera://photo.jpg', type: 'image/jpeg', fileName: 'photo.jpg' }]
        });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(mockLaunchCamera).toHaveBeenCalled();
        expect(mockOnUpload).toHaveBeenCalledWith('camera://photo.jpg');
      });
    });

    it('should handle camera cancellation', async () => {
      const mockLaunchCamera = require('react-native-image-picker').launchCamera;
      mockLaunchCamera.mockImplementation((options, callback) => {
        callback({ didCancel: true });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(mockLaunchCamera).toHaveBeenCalled();
        expect(mockOnUpload).not.toHaveBeenCalled();
      });
    });

    it('should handle camera errors', async () => {
      const mockLaunchCamera = require('react-native-image-picker').launchCamera;
      mockLaunchCamera.mockImplementation((options, callback) => {
        callback({ errorMessage: 'Camera not available' });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('camera-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          'Camera not available'
        );
      });
    });
  });

  describe('Photo Library Functionality', () => {
    it('should handle photo library button press', async () => {
      const mockLaunchImageLibrary = require('react-native-image-picker').launchImageLibrary;
      mockLaunchImageLibrary.mockImplementation((options, callback) => {
        callback({
          didCancel: false,
          assets: [{ uri: 'library://photo.jpg', type: 'image/jpeg', fileName: 'photo.jpg' }]
        });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('library-button'));

      await waitFor(() => {
        expect(mockLaunchImageLibrary).toHaveBeenCalled();
        expect(mockOnUpload).toHaveBeenCalledWith('library://photo.jpg');
      });
    });

    it('should handle photo library cancellation', async () => {
      const mockLaunchImageLibrary = require('react-native-image-picker').launchImageLibrary;
      mockLaunchImageLibrary.mockImplementation((options, callback) => {
        callback({ didCancel: true });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('library-button'));

      await waitFor(() => {
        expect(mockLaunchImageLibrary).toHaveBeenCalled();
        expect(mockOnUpload).not.toHaveBeenCalled();
      });
    });

    it('should handle photo library errors', async () => {
      const mockLaunchImageLibrary = require('react-native-image-picker').launchImageLibrary;
      mockLaunchImageLibrary.mockImplementation((options, callback) => {
        callback({ errorMessage: 'Library access denied' });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('library-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          'Library access denied'
        );
      });
    });
  });

  describe('Delete Functionality', () => {
    it('should handle delete button press', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
          hasCurrentAvatar={true}
        />
      );

      fireEvent.press(screen.getByTestId('delete-button'));

      expect(mockOnDelete).toHaveBeenCalled();
    });

    it('should show confirmation dialog for delete', async () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
          hasCurrentAvatar={true}
          confirmDelete={true}
        />
      );

      fireEvent.press(screen.getByTestId('delete-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Delete Avatar',
          'Are you sure you want to remove your profile picture?',
          expect.any(Array)
        );
      });
    });
  });

  describe('Cancel Functionality', () => {
    it('should handle cancel button press', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('cancel-button'));

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    it('should display loading state during upload', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
          isLoading={true}
        />
      );

      expect(screen.getByTestId('loading-indicator')).toBeTruthy();
      
      // Buttons should be disabled during loading
      expect(screen.getByTestId('camera-button').props.disabled).toBe(true);
      expect(screen.getByTestId('library-button').props.disabled).toBe(true);
    });

    it('should show upload progress when available', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
          isLoading={true}
          uploadProgress={0.5}
        />
      );

      expect(screen.getByTestId('upload-progress')).toBeTruthy();
      expect(screen.getByText('50%')).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByLabelText('Take photo with camera')).toBeTruthy();
      expect(screen.getByLabelText('Choose photo from library')).toBeTruthy();
      expect(screen.getByLabelText('Delete current avatar')).toBeTruthy();
      expect(screen.getByLabelText('Cancel avatar upload')).toBeTruthy();
    });

    it('should have proper modal accessibility', () => {
      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      const modal = screen.getByTestId('avatar-upload-modal');
      expect(modal.props.accessibilityRole).toBe('dialog');
      expect(modal.props.accessibilityLabel).toBe('Avatar upload options');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing image assets gracefully', async () => {
      const mockLaunchImageLibrary = require('react-native-image-picker').launchImageLibrary;
      mockLaunchImageLibrary.mockImplementation((options, callback) => {
        callback({ didCancel: false, assets: [] });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('library-button'));

      await waitFor(() => {
        expect(mockOnUpload).not.toHaveBeenCalled();
      });
    });

    it('should handle malformed image response', async () => {
      const mockLaunchImageLibrary = require('react-native-image-picker').launchImageLibrary;
      mockLaunchImageLibrary.mockImplementation((options, callback) => {
        callback({ didCancel: false, assets: [{ uri: null }] });
      });

      render(
        <AvatarUpload
          visible={true}
          onUpload={mockOnUpload}
          onCancel={mockOnCancel}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.press(screen.getByTestId('library-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          'Invalid image selected. Please try again.'
        );
      });
    });
  });
});
