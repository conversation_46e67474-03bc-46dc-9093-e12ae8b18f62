

# **Vierla Application: A Comprehensive Color System**

## **1.0 Introduction & Philosophy**

This document outlines the official color system for the Vierla mobile application. The palette is engineered to support the core philosophy of a "digital sanctuary"—an experience that is calming, trustworthy, and sophisticated.

The system is built on a foundation of nature-inspired hues, chosen for their psychological resonance and tested for accessibility. Every color has a defined role, ensuring a consistent, intuitive, and visually harmonious user interface across both iOS and Android platforms. This system provides a clear, actionable guide for designers and developers to create an experience that feels both premium and serene.

## **2.0 The Core Palette**

The core palette consists of five foundational colors that define the application's primary look and feel. Each color is assigned a specific role to create a clear visual hierarchy and guide user interaction.

| Role | Color | Hex | Swatch |
| :---- | :---- | :---- | :---- |
| **Primary** | Forest Green | \#364035 | 🌲 |
| **Secondary** | Sage Green | \#8B9A8C | 🌿 |
| **Background** | Warm Cream | \#F4F1E8 | 📜 |
| **Text** | Deep Charcoal | \#2D2A26 | ✒️ |
| **Accent** | Rich Gold | \#B8956A | ✨ |

### **2.1 Color Psychology & Rationale**

* **Primary \- <PERSON> (\#364035):** This deep, stable green serves as the anchor for all interactive elements. Psychologically, green is the color of balance, harmony, and growth.1 Darker shades like forest green evoke a sense of sophistication, security, and trust, making it the ideal choice for primary actions like "Confirm Booking".3 Its use signals reliability and encourages user confidence.  
* **Secondary \- Sage Green (\#8B9A8C):** A muted, calming green used for secondary actions and contextual information. Sage green is associated with tranquility and peace, providing a softer, less demanding alternative to the primary green.5 It supports the visual hierarchy by offering a clear distinction for less critical interactive elements like filters or inactive states.  
* **Background \- Warm Cream (\#F4F1E8):** This color provides a soft, soothing canvas for the entire application. Unlike stark white, warm cream is associated with comfort, elegance, and warmth, reducing eye strain and creating a more inviting atmosphere.6 It promotes a sense of understated luxury and calm, aligning perfectly with the self-care theme.9  
* **Text \- Deep Charcoal (\#2D2A26):** Chosen as a sophisticated and more accessible alternative to pure black. High-contrast black-on-white text can cause visual fatigue. Deep Charcoal softens this while maintaining excellent readability. Psychologically, charcoal is perceived as formal, stable, and professional, conveying information with clarity and authority.10  
* **Accent \- Rich Gold (\#B8956A):** A color to be used with intention and restraint. Gold is universally associated with success, achievement, and luxury.12 Its use will be reserved for moments of high value and positive reinforcement, such as a successful booking confirmation or highlighting a "Top Rated" professional. Overuse can appear pretentious, so its strategic application ensures it feels meaningful and special.14

## **3.0 The Systematic Scale: Tints & Shades**

The provided monochromatic green scale is a foundational tool for building a modern, interactive UI. Its purpose is not to introduce new primary colors but to provide a systematic range of tints (lighter versions) and shades (darker versions) for creating depth, hierarchy, and responsive feedback.16

This scale will be used to define component states (e.g., a button darkening when pressed), subtle borders, dividers, and background fills for elements like input fields or selected items.

| Name | Hex |
| :---- | :---- |
| Green-900 | \#171c17 |
| Green-800 | \#2d342d |
| Green-700 | \#364035 |
| Green-600 | \#3d493c |
| Green-500 | \#495a47 |
| Green-400 | \#5e715b |
| Green-300 | \#798c75 |
| Green-200 | \#9dad9b |
| Green-100 | \#c4cec1 |
| Green-50 | \#e2e7e0 |
| Green-25 | \#f6f7f6 |

## **4.0 Semantic & Utility Colors**

Semantic colors communicate specific, universal meanings to the user, such as success, error, or warning. These colors are designed to be instantly recognizable while harmonizing with the core palette.

| Role | Color | Hex | Swatch |
| :---- | :---- | :---- | :---- |
| **Success** | Sea Green | \#2E8540 | ✅ |
| **Error** | Terracotta | \#C14A4A | ❌ |
| **Warning** | Amber | \#D97E00 | ⚠️ |

## **5.0 Accessibility & WCAG 2.2 Compliance**

Accessibility is a non-negotiable requirement. All color combinations used for text and essential UI components must meet the Web Content Accessibility Guidelines (WCAG) AA level standards to be considered compliant.18 The following matrix provides a definitive guide for designers and developers.

**WCAG AA Standards:**

* **Normal Text (\<18pt):** Requires a contrast ratio of at least **4.5:1**.  
* **Large Text (≥18pt or ≥14pt Bold):** Requires a contrast ratio of at least **3:1**.  
* **UI Components (Non-text):** Requires a contrast ratio of at least **3:1**.20

### **5.1 Contrast Compliance Matrix**

| Foreground Element | Hex | Background Element | Hex | Contrast Ratio | WCAG AA (Normal) | WCAG AA (Large) | Usage Rule |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| **Text** | \#2D2A26 | Background | \#F4F1E8 | **12.98:1** | ✅ Pass | ✅ Pass | **Approved** for all text. |
| **Inverted Text** | \#F4F1E8 | Primary | \#364035 | **13.56:1** | ✅ Pass | ✅ Pass | **Approved** for text on primary buttons. |
| **Secondary Text** | \#2D2A26 | Secondary | \#8B9A8C | **3.39:1** | ❌ Fail | ✅ Pass | **Large Text Only.** Use for titles or bold text ≥14pt. |
| **Accent Text** | \#B8956A | Background | \#F4F1E8 | **3.05:1** | ❌ Fail | ✅ Pass | **Large/Decorative Text Only.** Not for body text. |
| **Error Text** | \#C14A4A | Background | \#F4F1E8 | **4.53:1** | ✅ Pass | ✅ Pass | **Approved** for error messages. |
| **Success Text** | \#2E8540 | Background | \#F4F1E8 | **6.67:1** | ✅ Pass | ✅ Pass | **Approved** for success messages. |
| **Disabled Text** | \#9dad9b | Background | \#F4F1E8 | **2.14:1** | ❌ Fail | ❌ Fail | **Not for text.** Use for disabled component backgrounds. |
| **Inverted Disabled** | \#c4cec1 | Disabled BG | \#e2e7e0 | **1.25:1** | ❌ Fail | ❌ Fail | **Not for text.** Use for disabled component text. |

## **6.0 Application Guide: Color Tokens**

To ensure consistent and scalable implementation in React Native, colors should be referenced via a theme provider using tokens, not hard-coded hex values. This allows for easy updates and the implementation of features like dark mode.

### **6.1 Light Mode Tokens**

| Token Name | Value | Description |
| :---- | :---- | :---- |
| color.primary.main | \#364035 | Main interactive elements, primary buttons. |
| color.primary.dark | \#2d342d | Pressed state for primary elements. |
| color.secondary.main | \#8B9A8C | Secondary buttons, active filters, tags. |
| color.accent.main | \#B8956A | High-value highlights, confirmations, ratings. |
| color.background.default | \#F4F1E8 | Main screen background. |
| color.background.subtle | \#f6f7f6 | Subtle backgrounds for cards, input fields. |
| color.text.primary | \#2D2A26 | Body copy, headlines. |
| color.text.secondary | \#5e715b | Subtitles, captions, less important text. |
| color.text.disabled | \#c4cec1 | Text on disabled components. |
| color.text.inverted | \#F4F1E8 | Text on dark backgrounds (e.g., primary buttons). |
| color.border.default | \#e2e7e0 | Default borders for cards and dividers. |
| color.border.interactive | \#9dad9b | Borders for interactive elements like text fields. |
| color.semantic.success | \#2E8540 | Success messages and icons. |
| color.semantic.error | \#C14A4A | Error messages, icons, and input field borders. |
| color.semantic.warning | \#D97E00 | Warning messages and icons. |
| color.component.disabled | \#e2e7e0 | Background for disabled components. |

### **6.2 Dark Mode Tokens**

Dark mode is not a simple inversion of colors. It requires a separate, carefully considered palette to reduce eye strain and maintain readability in low-light environments.21 The principles are:

1. **Avoid Pure Black:** The main background is a very dark gray (\#171c17) to reduce harsh contrast.  
2. **Desaturate Colors:** Primary and accent colors are desaturated to prevent them from "vibrating" against the dark background.  
3. **Ensure Contrast:** All text and interactive elements are tested again for WCAG compliance against the new dark surfaces.

| Token Name | Value | Dark Mode Contrast (vs. \#171c17) |
| :---- | :---- | :---- |
| color.primary.main | \#8B9A8C | 5.35:1 |
| color.primary.dark | \#798c75 | 4.22:1 |
| color.secondary.main | \#495a47 | 2.45:1 (UI Components Only) |
| color.accent.main | \#B8956A | 6.55:1 |
| color.background.default | \#171c17 | \- |
| color.background.subtle | \#2d342d | 1.54:1 (UI Components Only) |
| color.text.primary | \#f6f7f6 | 16.51:1 |
| color.text.secondary | \#9dad9b | 5.01:1 |
| color.text.disabled | \#495a47 | 2.45:1 |
| color.text.inverted | \#171c17 | 5.35:1 (on primary.main) |
| color.border.default | \#3d493c | 1.93:1 |
| color.border.interactive | \#798c75 | 4.22:1 |
| color.semantic.success | \#5CB85C | 5.53:1 |
| color.semantic.error | \#D9534F | 4.54:1 |
| color.semantic.warning | \#F0AD4E | 7.94:1 |
| color.component.disabled | \#364035 | 1.79:1 |

#### **Works cited**

1. The Color Green \- Empower Yourself with Color Psychology, accessed August 7, 2025, [https://www.empower-yourself-with-color-psychology.com/color-green.html](https://www.empower-yourself-with-color-psychology.com/color-green.html)  
2. www.empower-yourself-with-color-psychology.com, accessed August 7, 2025, [https://www.empower-yourself-with-color-psychology.com/color-green.html\#:\~:text=The%20color%20green%20relates%20to,spring%2C%20of%20renewal%20and%20rebirth.](https://www.empower-yourself-with-color-psychology.com/color-green.html#:~:text=The%20color%20green%20relates%20to,spring%2C%20of%20renewal%20and%20rebirth.)  
3. Green Magic: The Psychology and Power of Nature's Color in Branding \- Medium, accessed August 7, 2025, [https://medium.com/design-bootcamp/green-magic-the-psychology-and-power-of-natures-color-in-branding-6e0405c9b346](https://medium.com/design-bootcamp/green-magic-the-psychology-and-power-of-natures-color-in-branding-6e0405c9b346)  
4. Green Color Psychology, Symbolism and Meaning, accessed August 7, 2025, [https://www.colorpsychology.org/green/](https://www.colorpsychology.org/green/)  
5. What Does the Color Green Mean? \- Verywell Mind, accessed August 7, 2025, [https://www.verywellmind.com/color-psychology-green-2795817](https://www.verywellmind.com/color-psychology-green-2795817)  
6. www.adobe.com, accessed August 7, 2025, [https://www.adobe.com/express/colors/cream\#:\~:text=The%20color%20cream%20is%20a,either%20white%20or%20cream%20color.](https://www.adobe.com/express/colors/cream#:~:text=The%20color%20cream%20is%20a,either%20white%20or%20cream%20color.)  
7. Cream Color: Meaning, Hex Code, 141 Palettes, accessed August 7, 2025, [https://theapplaunchpad.com/color-meanings/cream](https://theapplaunchpad.com/color-meanings/cream)  
8. All about the Color Cream \- Meaning, Psychology & Design Tips \- Picsart, accessed August 7, 2025, [https://picsart.com/colors/color-meanings/cream/](https://picsart.com/colors/color-meanings/cream/)  
9. Creams and Off Whites (Psychological Effect in Interior), accessed August 7, 2025, [https://cherdecor.com/en/blog/neutral-colors/creams-and-off-whites](https://cherdecor.com/en/blog/neutral-colors/creams-and-off-whites)  
10. www.adobe.com, accessed August 7, 2025, [https://www.adobe.com/express/colors/charcoal\#:\~:text=Tap%20into%20the%20psychology%20of,professionalism%20or%20influence%20are%20central.](https://www.adobe.com/express/colors/charcoal#:~:text=Tap%20into%20the%20psychology%20of,professionalism%20or%20influence%20are%20central.)  
11. The Color Charcoal | Adobe Express, accessed August 7, 2025, [https://www.adobe.com/express/colors/charcoal](https://www.adobe.com/express/colors/charcoal)  
12. All about the Color Gold \- Meaning & Psychology \- Picsart, accessed August 7, 2025, [https://picsart.com/colors/color-meanings/gold/](https://picsart.com/colors/color-meanings/gold/)  
13. The Color Gold, accessed August 7, 2025, [https://www.empower-yourself-with-color-psychology.com/color-gold.html](https://www.empower-yourself-with-color-psychology.com/color-gold.html)  
14. Gold Color Meaning and Psychology in Design, accessed August 7, 2025, [https://octet.design/journal/gold-color-meaning/](https://octet.design/journal/gold-color-meaning/)  
15. Gold in Business \- Empower Yourself with Color Psychology, accessed August 7, 2025, [https://www.empower-yourself-with-color-psychology.com/gold-in-business.html](https://www.empower-yourself-with-color-psychology.com/gold-in-business.html)  
16. Paletton \- The Color Scheme Designer, accessed August 7, 2025, [https://paletton.com/](https://paletton.com/)  
17. How to effectively design a monochromatic user interface | by Madison Mariani, accessed August 7, 2025, [https://uxdesign.cc/how-to-effectively-design-a-monochromatic-user-interface-ui-897c07c5a09e](https://uxdesign.cc/how-to-effectively-design-a-monochromatic-user-interface-ui-897c07c5a09e)  
18. WCAG Color Contrast Checker \- Accessible Web, accessed August 7, 2025, [https://accessibleweb.com/color-contrast-checker/](https://accessibleweb.com/color-contrast-checker/)  
19. Accessibility Color Contrast Checker WCAG Compliance (2025), accessed August 7, 2025, [https://www.accessibilitychecker.org/color-contrast-checker/](https://www.accessibilitychecker.org/color-contrast-checker/)  
20. Colour Contrast Analyser (CCA) \- TPGi, accessed August 7, 2025, [https://www.tpgi.com/color-contrast-checker/](https://www.tpgi.com/color-contrast-checker/)  
21. Mastering Dark Mode UI: Essential Tips for Effective Design \- Five Jars, accessed August 7, 2025, [https://fivejars.com/insights/dark-mode-ui-9-design-considerations-you-cant-ignore/](https://fivejars.com/insights/dark-mode-ui-9-design-considerations-you-cant-ignore/)  
22. 10 Dark Mode UI Best Practices & Principles, accessed August 7, 2025, [https://www.designstudiouiux.com/blog/dark-mode-ui-design-best-practices/](https://www.designstudiouiux.com/blog/dark-mode-ui-design-best-practices/)