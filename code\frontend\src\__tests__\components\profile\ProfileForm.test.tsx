/**
 * ProfileForm Component Tests
 * Comprehensive test suite for profile editing form component
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { ProfileForm } from '../../../components/profile/ProfileForm';
import { User, UserProfile } from '../../../services/api/profile';

// Mock UI components
jest.mock('../../../components/ui/AnimatedCard', () => ({
  AnimatedCard: ({ children, ...props }: any) => <div {...props}>{children}</div>,
}));

jest.mock('../../../components/ui/Button', () => ({
  Button: ({ children, onPress, testID, ...props }: any) => (
    <button testID={testID} onPress={onPress} {...props}>{children}</button>
  ),
}));

jest.mock('../../../components/ui/ModernInput', () => ({
  ModernInput: ({ testID, onChangeText, value, ...props }: any) => (
    <input
      testID={testID}
      onChange={(e) => onChangeText?.(e.target.value)}
      value={value}
      {...props}
    />
  ),
}));

describe('ProfileForm', () => {
  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    username: 'testuser',
    first_name: 'John',
    last_name: 'Doe',
    full_name: 'John Doe',
    phone: '+**********',
    role: 'customer',
    avatar: 'https://example.com/avatar.jpg',
    date_of_birth: '1990-01-01',
    bio: 'Test bio',
    account_status: 'active',
    is_verified: true,
    email_verified_at: '2023-01-01T00:00:00Z',
    phone_verified_at: '2023-01-01T00:00:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  const mockProfile: UserProfile = {
    address: '123 Main St',
    city: 'New York',
    state: 'NY',
    zip_code: '10001',
    country: 'USA',
    business_name: 'Test Business',
    business_description: 'A test business',
    years_of_experience: 5,
    website: 'https://testbusiness.com',
    search_radius: 25,
    auto_accept_bookings: true,
    show_phone_publicly: false,
    show_email_publicly: true,
    allow_reviews: true,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  const mockOnSave = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Form Initialization', () => {
    it('should initialize form with user data', () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByDisplayValue('John')).toBeTruthy();
      expect(screen.getByDisplayValue('Doe')).toBeTruthy();
      expect(screen.getByDisplayValue('+**********')).toBeTruthy();
      expect(screen.getByDisplayValue('Test bio')).toBeTruthy();
      expect(screen.getByDisplayValue('1990-01-01')).toBeTruthy();
    });

    it('should initialize form with empty values when user is null', () => {
      render(
        <ProfileForm 
          user={null} 
          profile={null}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByTestId('profile-form')).toBeTruthy();
      // Form should render with empty fields
    });

    it('should display role-specific fields for providers', () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };
      
      render(
        <ProfileForm 
          user={providerUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByDisplayValue('Test Business')).toBeTruthy();
      expect(screen.getByDisplayValue('A test business')).toBeTruthy();
      expect(screen.getByDisplayValue('5')).toBeTruthy();
      expect(screen.getByDisplayValue('https://testbusiness.com')).toBeTruthy();
    });

    it('should not display business fields for customers', () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.queryByDisplayValue('Test Business')).toBeFalsy();
      expect(screen.queryByDisplayValue('A test business')).toBeFalsy();
    });
  });

  describe('Form Input Handling', () => {
    it('should update first name field', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const firstNameInput = screen.getByTestId('first-name-input');
      fireEvent.changeText(firstNameInput, 'Jane');

      await waitFor(() => {
        expect(firstNameInput.props.value).toBe('Jane');
      });
    });

    it('should update last name field', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const lastNameInput = screen.getByTestId('last-name-input');
      fireEvent.changeText(lastNameInput, 'Smith');

      await waitFor(() => {
        expect(lastNameInput.props.value).toBe('Smith');
      });
    });

    it('should update phone field', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const phoneInput = screen.getByTestId('phone-input');
      fireEvent.changeText(phoneInput, '+1987654321');

      await waitFor(() => {
        expect(phoneInput.props.value).toBe('+1987654321');
      });
    });

    it('should update bio field', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const bioInput = screen.getByTestId('bio-input');
      fireEvent.changeText(bioInput, 'Updated bio');

      await waitFor(() => {
        expect(bioInput.props.value).toBe('Updated bio');
      });
    });

    it('should update date of birth field', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const dobInput = screen.getByTestId('date-of-birth-input');
      fireEvent.changeText(dobInput, '1985-05-15');

      await waitFor(() => {
        expect(dobInput.props.value).toBe('1985-05-15');
      });
    });
  });

  describe('Form Validation', () => {
    it('should validate required fields', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      // Clear required fields
      const firstNameInput = screen.getByTestId('first-name-input');
      fireEvent.changeText(firstNameInput, '');

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(screen.getByText('First name is required')).toBeTruthy();
      });
    });

    it('should validate phone number format', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const phoneInput = screen.getByTestId('phone-input');
      fireEvent.changeText(phoneInput, '**********'); // Missing country code

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Phone number must include country code (e.g., +**********)')).toBeTruthy();
      });
    });

    it('should validate bio length', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const bioInput = screen.getByTestId('bio-input');
      fireEvent.changeText(bioInput, 'A'.repeat(501)); // Exceeds 500 character limit

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Bio cannot exceed 500 characters')).toBeTruthy();
      });
    });

    it('should validate date format', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const dobInput = screen.getByTestId('date-of-birth-input');
      fireEvent.changeText(dobInput, '01/01/1990'); // Wrong format

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Date of birth must be in YYYY-MM-DD format')).toBeTruthy();
      });
    });

    it('should clear validation errors when fields are corrected', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      // Create validation error
      const phoneInput = screen.getByTestId('phone-input');
      fireEvent.changeText(phoneInput, '**********');

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Phone number must include country code (e.g., +**********)')).toBeTruthy();
      });

      // Fix the error
      fireEvent.changeText(phoneInput, '+**********');

      await waitFor(() => {
        expect(screen.queryByText('Phone number must include country code (e.g., +**********)')).toBeFalsy();
      });
    });
  });

  describe('Form Submission', () => {
    it('should call onSave with updated data when form is valid', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const firstNameInput = screen.getByTestId('first-name-input');
      fireEvent.changeText(firstNameInput, 'Jane');

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalledWith(
          expect.objectContaining({
            first_name: 'Jane',
          })
        );
      });
    });

    it('should not call onSave when form has validation errors', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const firstNameInput = screen.getByTestId('first-name-input');
      fireEvent.changeText(firstNameInput, ''); // Make it invalid

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(mockOnSave).not.toHaveBeenCalled();
      });
    });

    it('should call onCancel when cancel button is pressed', () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const cancelButton = screen.getByTestId('cancel-button');
      fireEvent.press(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    it('should display loading state when saving', async () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          isLoading={true}
        />
      );

      expect(screen.getByTestId('loading-indicator')).toBeTruthy();
      
      const saveButton = screen.getByTestId('save-button');
      expect(saveButton.props.disabled).toBe(true);
    });

    it('should disable form inputs when loading', () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          isLoading={true}
        />
      );

      const firstNameInput = screen.getByTestId('first-name-input');
      expect(firstNameInput.props.editable).toBe(false);
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByLabelText('First name')).toBeTruthy();
      expect(screen.getByLabelText('Last name')).toBeTruthy();
      expect(screen.getByLabelText('Phone number')).toBeTruthy();
      expect(screen.getByLabelText('Biography')).toBeTruthy();
      expect(screen.getByLabelText('Date of birth')).toBeTruthy();
    });

    it('should have proper form structure', () => {
      render(
        <ProfileForm 
          user={mockUser} 
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const form = screen.getByTestId('profile-form');
      expect(form.props.accessibilityRole).toBe('form');
    });
  });

  describe('Business Fields for Providers', () => {
    it('should display business fields for service providers', () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };

      render(
        <ProfileForm
          user={providerUser}
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByTestId('business-name-input')).toBeTruthy();
      expect(screen.getByTestId('business-description-input')).toBeTruthy();
      expect(screen.getByTestId('years-experience-input')).toBeTruthy();
      expect(screen.getByTestId('website-input')).toBeTruthy();
    });

    it('should update business fields correctly', async () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };

      render(
        <ProfileForm
          user={providerUser}
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const businessNameInput = screen.getByTestId('business-name-input');
      fireEvent.changeText(businessNameInput, 'New Business Name');

      await waitFor(() => {
        expect(businessNameInput.props.value).toBe('New Business Name');
      });
    });

    it('should validate website URL format', async () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };

      render(
        <ProfileForm
          user={providerUser}
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const websiteInput = screen.getByTestId('website-input');
      fireEvent.changeText(websiteInput, 'invalid-url');

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Website must be a valid URL starting with http:// or https://')).toBeTruthy();
      });
    });

    it('should validate years of experience range', async () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };

      render(
        <ProfileForm
          user={providerUser}
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const experienceInput = screen.getByTestId('years-experience-input');
      fireEvent.changeText(experienceInput, '51'); // Exceeds 50 year limit

      const saveButton = screen.getByTestId('save-button');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Years of experience must be between 0 and 50')).toBeTruthy();
      });
    });
  });

  describe('Location Fields', () => {
    it('should display location fields', () => {
      render(
        <ProfileForm
          user={mockUser}
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByTestId('address-input')).toBeTruthy();
      expect(screen.getByTestId('city-input')).toBeTruthy();
      expect(screen.getByTestId('state-input')).toBeTruthy();
      expect(screen.getByTestId('zip-code-input')).toBeTruthy();
      expect(screen.getByTestId('country-input')).toBeTruthy();
    });

    it('should update location fields correctly', async () => {
      render(
        <ProfileForm
          user={mockUser}
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const addressInput = screen.getByTestId('address-input');
      fireEvent.changeText(addressInput, '456 New Street');

      await waitFor(() => {
        expect(addressInput.props.value).toBe('456 New Street');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle missing user data gracefully', () => {
      render(
        <ProfileForm
          user={null}
          profile={null}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByTestId('profile-form')).toBeTruthy();
      // Should not crash and should render empty form
    });

    it('should handle partial user data', () => {
      const partialUser = {
        ...mockUser,
        first_name: '',
        last_name: '',
        phone: undefined,
        bio: undefined,
        date_of_birth: undefined,
      };

      render(
        <ProfileForm
          user={partialUser}
          profile={mockProfile}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByTestId('profile-form')).toBeTruthy();
      // Should render with empty fields for missing data
    });
  });
});
