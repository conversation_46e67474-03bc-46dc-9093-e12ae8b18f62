/**
 * LocationInput Component
 * Enhanced input component with address autocomplete and location services
 * Provides geocoding and reverse geocoding functionality
 */

import React, { useState, useCallback, useRef } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { ModernInput } from './ModernInput';
import { BodyText, CaptionText } from './Typography';

interface LocationSuggestion {
  id: string;
  description: string;
  address: string;
  city: string;
  state: string;
  country: string;
  latitude?: number;
  longitude?: number;
}

interface LocationInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  onLocationSelect?: (location: LocationSuggestion) => void;
  error?: string;
  helperText?: string;
  placeholder?: string;
  testID?: string;
  showCurrentLocationButton?: boolean;
}

export const LocationInput: React.FC<LocationInputProps> = ({
  label,
  value,
  onChangeText,
  onLocationSelect,
  error,
  helperText,
  placeholder,
  testID,
  showCurrentLocationButton = true,
}) => {
  const { colors, spacing, borderRadius } = useTheme();
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  const styles = StyleSheet.create({
    container: {
      position: 'relative',
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    locationButton: {
      position: 'absolute',
      right: 8,
      top: '50%',
      transform: [{ translateY: -12 }],
      padding: 8,
      borderRadius: borderRadius.sm,
      backgroundColor: colors.primary,
    },
    suggestionsContainer: {
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      backgroundColor: colors.background.primary,
      borderRadius: borderRadius.md,
      borderWidth: 1,
      borderColor: colors.extended.sageVariant,
      maxHeight: 200,
      zIndex: 1000,
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    suggestionItem: {
      padding: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.extended.sageVariant,
    },
    suggestionText: {
      fontSize: 14,
      color: colors.text.primary,
    },
    suggestionSubtext: {
      fontSize: 12,
      color: colors.text.secondary,
      marginTop: 2,
    },
    loadingContainer: {
      padding: spacing.md,
      alignItems: 'center',
    },
  });

  // Mock geocoding service (in production, use Google Places API or similar)
  const searchLocations = useCallback(async (query: string): Promise<LocationSuggestion[]> => {
    // Mock data for demonstration
    const mockSuggestions: LocationSuggestion[] = [
      {
        id: '1',
        description: `${query} Street, Toronto, ON`,
        address: `${query} Street`,
        city: 'Toronto',
        state: 'ON',
        country: 'Canada',
        latitude: 43.6532,
        longitude: -79.3832,
      },
      {
        id: '2',
        description: `${query} Avenue, Vancouver, BC`,
        address: `${query} Avenue`,
        city: 'Vancouver',
        state: 'BC',
        country: 'Canada',
        latitude: 49.2827,
        longitude: -123.1207,
      },
      {
        id: '3',
        description: `${query} Road, Montreal, QC`,
        address: `${query} Road`,
        city: 'Montreal',
        state: 'QC',
        country: 'Canada',
        latitude: 45.5017,
        longitude: -73.5673,
      },
    ];

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return mockSuggestions.filter(suggestion => 
      suggestion.description.toLowerCase().includes(query.toLowerCase())
    );
  }, []);

  const handleTextChange = useCallback((text: string) => {
    onChangeText(text);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Hide suggestions if text is empty
    if (!text.trim()) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    // Debounce search
    searchTimeoutRef.current = setTimeout(async () => {
      if (text.length >= 3) {
        setIsLoading(true);
        try {
          const results = await searchLocations(text);
          setSuggestions(results);
          setShowSuggestions(true);
        } catch (error) {
          console.error('Location search error:', error);
          setSuggestions([]);
        } finally {
          setIsLoading(false);
        }
      }
    }, 300);
  }, [onChangeText, searchLocations]);

  const handleSuggestionSelect = useCallback((suggestion: LocationSuggestion) => {
    onChangeText(suggestion.description);
    onLocationSelect?.(suggestion);
    setShowSuggestions(false);
    setSuggestions([]);
  }, [onChangeText, onLocationSelect]);

  const getCurrentLocation = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Location permission is needed to get your current address.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Reverse geocode to get address
      const addresses = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      if (addresses.length > 0) {
        const address = addresses[0];
        const fullAddress = [
          address.streetNumber,
          address.street,
          address.city,
          address.region,
        ].filter(Boolean).join(' ');

        const locationData: LocationSuggestion = {
          id: 'current',
          description: fullAddress,
          address: `${address.streetNumber || ''} ${address.street || ''}`.trim(),
          city: address.city || '',
          state: address.region || '',
          country: address.country || '',
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        };

        onChangeText(fullAddress);
        onLocationSelect?.(locationData);
      }
    } catch (error) {
      console.error('Get current location error:', error);
      Alert.alert(
        'Location Error',
        'Unable to get your current location. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  }, [onChangeText, onLocationSelect]);

  const renderSuggestion = ({ item }: { item: LocationSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleSuggestionSelect(item)}
      testID={`location-suggestion-${item.id}`}
    >
      <BodyText style={styles.suggestionText}>{item.description}</BodyText>
      <CaptionText style={styles.suggestionSubtext}>
        {item.city}, {item.state}, {item.country}
      </CaptionText>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        <ModernInput
          label={label}
          value={value}
          onChangeText={handleTextChange}
          error={error}
          helperText={helperText}
          placeholder={placeholder}
          testID={testID}
          containerStyle={{ flex: 1 }}
        />
        
        {showCurrentLocationButton && (
          <TouchableOpacity
            style={styles.locationButton}
            onPress={getCurrentLocation}
            disabled={isLoading}
            testID="current-location-button"
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.background.primary} />
            ) : (
              <Ionicons name="location" size={16} color={colors.background.primary} />
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* Suggestions Dropdown */}
      {showSuggestions && (
        <View style={styles.suggestionsContainer}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <CaptionText style={{ marginTop: 8 }}>Searching locations...</CaptionText>
            </View>
          ) : (
            <FlatList
              data={suggestions}
              renderItem={renderSuggestion}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              testID="location-suggestions-list"
            />
          )}
        </View>
      )}
    </View>
  );
};

export default LocationInput;
