/**
 * Profile API Service Tests
 * Comprehensive test suite for profile API service layer
 */

import { profile<PERSON><PERSON>, User, UserProfile, ProfileUpdateRequest, ProfileDetailsUpdateRequest, ImageAsset, APIError } from '../../../services/api/profile';
import { apiClient } from '../../../services/api/client';

// Mock the API client
jest.mock('../../../services/api/client', () => ({
  apiClient: {
    get: jest.fn(),
    patch: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('Profile API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getProfile', () => {
    const mockUser: User = {
      id: '1',
      email: '<EMAIL>',
      username: 'testuser',
      first_name: '<PERSON>',
      last_name: 'Doe',
      full_name: 'John Doe',
      phone: '+**********',
      role: 'customer',
      avatar: 'https://example.com/avatar.jpg',
      date_of_birth: '1990-01-01',
      bio: 'Test bio',
      account_status: 'active',
      is_verified: true,
      email_verified_at: '2023-01-01T00:00:00Z',
      phone_verified_at: '2023-01-01T00:00:00Z',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    };

    it('should successfully fetch user profile', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockUser });

      const result = await profileAPI.getProfile();

      expect(mockApiClient.get).toHaveBeenCalledWith('/auth/profile/');
      expect(result).toEqual(mockUser);
    });

    it('should handle API errors when fetching profile', async () => {
      const mockError = new Error('Network error');
      mockApiClient.get.mockRejectedValue(mockError);

      await expect(profileAPI.getProfile()).rejects.toMatchObject({
        message: 'Network error',
      });
      expect(console.error).toHaveBeenCalledWith('Get profile error:', mockError);
    });

    it('should handle 401 unauthorized error', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { detail: 'Authentication credentials were not provided.' }
        }
      };
      mockApiClient.get.mockRejectedValue(mockError);

      await expect(profileAPI.getProfile()).rejects.toMatchObject({
        message: 'Authentication credentials were not provided.',
        status: 401,
        field_errors: { detail: 'Authentication credentials were not provided.' },
      });
    });

    it('should handle 404 not found error', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { detail: 'User not found.' }
        }
      };
      mockApiClient.get.mockRejectedValue(mockError);

      await expect(profileAPI.getProfile()).rejects.toMatchObject({
        message: 'User not found.',
        status: 404,
        field_errors: { detail: 'User not found.' },
      });
    });
  });

  describe('updateProfile', () => {
    const mockUpdateRequest: ProfileUpdateRequest = {
      first_name: 'Jane',
      last_name: 'Smith',
      phone: '+**********',
      bio: 'Updated bio',
      date_of_birth: '1992-05-15',
    };

    const mockUpdatedUser: User = {
      id: '1',
      email: '<EMAIL>',
      username: 'testuser',
      first_name: 'Jane',
      last_name: 'Smith',
      full_name: 'Jane Smith',
      phone: '+**********',
      role: 'customer',
      avatar: 'https://example.com/avatar.jpg',
      date_of_birth: '1992-05-15',
      bio: 'Updated bio',
      account_status: 'active',
      is_verified: true,
      email_verified_at: '2023-01-01T00:00:00Z',
      phone_verified_at: '2023-01-01T00:00:00Z',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-02T00:00:00Z',
    };

    it('should successfully update user profile', async () => {
      mockApiClient.patch.mockResolvedValue({ data: mockUpdatedUser });

      const result = await profileAPI.updateProfile(mockUpdateRequest);

      expect(mockApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', mockUpdateRequest);
      expect(result).toEqual(mockUpdatedUser);
    });

    it('should handle validation errors when updating profile', async () => {
      const mockError = {
        response: {
          status: 400,
          data: {
            phone: ['Phone number must include country code (e.g., +**********)'],
            bio: ['Bio cannot exceed 500 characters']
          }
        }
      };
      mockApiClient.patch.mockRejectedValue(mockError);

      await expect(profileAPI.updateProfile(mockUpdateRequest)).rejects.toMatchObject({
        message: 'An error occurred',
        status: 400,
        field_errors: {
          phone: ['Phone number must include country code (e.g., +**********)'],
          bio: ['Bio cannot exceed 500 characters']
        },
      });
      expect(console.error).toHaveBeenCalledWith('Update profile error:', mockError);
    });

    it('should handle partial updates', async () => {
      const partialUpdate = { first_name: 'UpdatedName' };
      const partialUpdatedUser = { ...mockUpdatedUser, first_name: 'UpdatedName' };
      
      mockApiClient.patch.mockResolvedValue({ data: partialUpdatedUser });

      const result = await profileAPI.updateProfile(partialUpdate);

      expect(mockApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', partialUpdate);
      expect(result.first_name).toBe('UpdatedName');
    });

    it('should handle empty update request', async () => {
      const emptyUpdate = {};
      mockApiClient.patch.mockResolvedValue({ data: mockUpdatedUser });

      const result = await profileAPI.updateProfile(emptyUpdate);

      expect(mockApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', emptyUpdate);
      expect(result).toEqual(mockUpdatedUser);
    });
  });

  describe('getProfileDetails', () => {
    it('should return null for unimplemented endpoint', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { detail: 'Not found' }
        }
      };
      mockApiClient.get.mockRejectedValue(mockError);

      const result = await profileAPI.getProfileDetails();

      expect(result).toBeNull();
      expect(console.warn).toHaveBeenCalledWith('getProfileDetails: Profile details endpoint not found, returning null');
    });

    it('should handle errors gracefully', async () => {
      // Mock implementation that throws an error
      const originalMethod = profileAPI.getProfileDetails;
      profileAPI.getProfileDetails = jest.fn().mockRejectedValue(new Error('Test error'));

      await expect(profileAPI.getProfileDetails()).rejects.toThrow('Test error');

      // Restore original method
      profileAPI.getProfileDetails = originalMethod;
    });
  });

  describe('updateProfileDetails', () => {
    const mockDetailsUpdate: ProfileDetailsUpdateRequest = {
      address: '123 Main St',
      city: 'New York',
      state: 'NY',
      zip_code: '10001',
      country: 'USA',
      business_name: 'Test Business',
      business_description: 'A test business',
      years_of_experience: 5,
      website: 'https://testbusiness.com',
      search_radius: 25,
      auto_accept_bookings: true,
      show_phone_publicly: false,
      show_email_publicly: true,
      allow_reviews: true,
    };

    it('should return null for unimplemented endpoint', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { detail: 'Not found' }
        }
      };
      mockApiClient.patch.mockRejectedValue(mockError);

      // Mock the updateProfile method to avoid actual calls
      const updateSpy = jest.spyOn(profileAPI, 'updateProfile').mockResolvedValue({} as User);

      const result = await profileAPI.updateProfileDetails(mockDetailsUpdate);

      expect(result).toBeNull();
      expect(console.warn).toHaveBeenCalledWith('updateProfileDetails: Profile details endpoint not found, using main profile endpoint');

      updateSpy.mockRestore();
    });

    it('should call updateProfile for business_name mapping when endpoint not found', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { detail: 'Not found' }
        }
      };
      mockApiClient.patch.mockRejectedValue(mockError);
      const updateSpy = jest.spyOn(profileAPI, 'updateProfile').mockResolvedValue({} as User);

      await profileAPI.updateProfileDetails({ business_name: 'Test Business' });

      expect(updateSpy).toHaveBeenCalledWith({ first_name: 'Test Business' });
      updateSpy.mockRestore();
    });

    it('should not call updateProfile when no mappable fields', async () => {
      const updateSpy = jest.spyOn(profileAPI, 'updateProfile').mockResolvedValue({} as User);
      
      await profileAPI.updateProfileDetails({ address: '123 Main St' });

      expect(updateSpy).not.toHaveBeenCalled();
      updateSpy.mockRestore();
    });
  });

  describe('uploadAvatar', () => {
    const mockImageAsset: ImageAsset = {
      uri: 'file://path/to/image.jpg',
      type: 'image/jpeg',
      name: 'avatar.jpg',
    };

    const mockUserWithAvatar: User = {
      id: '1',
      email: '<EMAIL>',
      username: 'testuser',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'John Doe',
      role: 'customer',
      avatar: 'https://example.com/new-avatar.jpg',
      account_status: 'active',
      is_verified: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-02T00:00:00Z',
    };

    it('should successfully upload avatar', async () => {
      mockApiClient.patch.mockResolvedValue({ data: mockUserWithAvatar });

      const result = await profileAPI.uploadAvatar(mockImageAsset);

      expect(mockApiClient.patch).toHaveBeenCalledWith(
        '/auth/profile/update/',
        expect.any(FormData),
        expect.objectContaining({
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: expect.any(Function),
        })
      );
      expect(result).toBe('https://example.com/new-avatar.jpg');
    });

    it('should return original URI when no avatar in response', async () => {
      const userWithoutAvatar = { ...mockUserWithAvatar, avatar: undefined };
      mockApiClient.patch.mockResolvedValue({ data: userWithoutAvatar });

      const result = await profileAPI.uploadAvatar(mockImageAsset);

      expect(result).toBe(mockImageAsset.uri);
    });

    it('should handle upload errors', async () => {
      const mockError = {
        response: {
          status: 413,
          data: { detail: 'File too large' }
        }
      };
      mockApiClient.patch.mockRejectedValue(mockError);

      await expect(profileAPI.uploadAvatar(mockImageAsset)).rejects.toMatchObject({
        message: 'File too large',
        status: 413,
        field_errors: { detail: 'File too large' },
      });
      expect(console.error).toHaveBeenCalledWith('Upload avatar error:', mockError);
    });

    it('should create proper FormData with image asset', async () => {
      mockApiClient.patch.mockResolvedValue({ data: mockUserWithAvatar });

      await profileAPI.uploadAvatar(mockImageAsset);

      const formDataCall = mockApiClient.patch.mock.calls[0];
      const formData = formDataCall[1] as FormData;
      
      expect(formData).toBeInstanceOf(FormData);
      // Note: FormData content testing is limited in Jest environment
    });
  });

  describe('deleteAvatar', () => {
    it('should successfully delete avatar', async () => {
      mockApiClient.patch.mockResolvedValue({ data: {} });

      await profileAPI.deleteAvatar();

      expect(mockApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', { avatar: null });
    });

    it('should handle delete errors', async () => {
      const mockError = new Error('Delete failed');
      mockApiClient.patch.mockRejectedValue(mockError);

      await expect(profileAPI.deleteAvatar()).rejects.toMatchObject({
        message: 'Delete failed',
      });
      expect(console.error).toHaveBeenCalledWith('Delete avatar error:', mockError);
    });
  });

  describe('handleError', () => {
    it('should handle response errors with detail', () => {
      const error = {
        response: {
          status: 400,
          data: { detail: 'Bad request' }
        }
      };

      const result = profileAPI.handleError(error);

      expect(result).toEqual({
        message: 'Bad request',
        status: 400,
        field_errors: { detail: 'Bad request' },
      });
    });

    it('should handle response errors with message', () => {
      const error = {
        response: {
          status: 500,
          data: { message: 'Internal server error' }
        }
      };

      const result = profileAPI.handleError(error);

      expect(result).toEqual({
        message: 'Internal server error',
        status: 500,
        field_errors: { message: 'Internal server error' },
      });
    });

    it('should handle response errors with field errors', () => {
      const error = {
        response: {
          status: 400,
          data: {
            errors: {
              phone: ['Invalid phone number'],
              email: ['Email already exists']
            }
          }
        }
      };

      const result = profileAPI.handleError(error);

      expect(result).toEqual({
        message: 'An error occurred',
        status: 400,
        field_errors: {
          phone: ['Invalid phone number'],
          email: ['Email already exists']
        },
      });
    });

    it('should handle network errors', () => {
      const error = {
        request: {}
      };

      const result = profileAPI.handleError(error);

      expect(result).toEqual({
        message: 'Network error - please check your connection',
        status: 0,
      });
    });

    it('should handle generic errors', () => {
      const error = new Error('Something went wrong');

      const result = profileAPI.handleError(error);

      expect(result).toEqual({
        message: 'Something went wrong',
      });
    });

    it('should handle errors without message', () => {
      const error = {};

      const result = profileAPI.handleError(error);

      expect(result).toEqual({
        message: 'An unexpected error occurred',
      });
    });
  });

  describe('transformUserData', () => {
    it('should transform user data with full_name', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        role: 'customer',
        avatar: 'https://example.com/avatar.jpg',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.transformUserData(user);

      expect(result).toEqual({
        ...user,
        displayName: 'John Doe',
        hasAvatar: true,
      });
    });

    it('should transform user data without full_name', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: '',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.transformUserData(user);

      expect(result).toEqual({
        ...user,
        displayName: 'John Doe',
        hasAvatar: false,
      });
    });

    it('should fallback to username when no names available', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: '',
        last_name: '',
        full_name: '',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.transformUserData(user);

      expect(result).toEqual({
        ...user,
        displayName: 'testuser',
        hasAvatar: false,
      });
    });

    it('should handle partial names correctly', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: '',
        full_name: '',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.transformUserData(user);

      expect(result).toEqual({
        ...user,
        displayName: 'John',
        hasAvatar: false,
      });
    });
  });

  describe('isProfileComplete', () => {
    it('should return true for complete profile', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.isProfileComplete(user);

      expect(result).toBe(true);
    });

    it('should return false for incomplete profile - missing first_name', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: '',
        last_name: 'Doe',
        full_name: 'Doe',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.isProfileComplete(user);

      expect(result).toBe(false);
    });

    it('should return false for incomplete profile - missing last_name', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: '',
        full_name: 'John',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.isProfileComplete(user);

      expect(result).toBe(false);
    });

    it('should return false for incomplete profile - missing email', () => {
      const user: User = {
        id: '1',
        email: '',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.isProfileComplete(user);

      expect(result).toBe(false);
    });
  });

  describe('validateProfileData', () => {
    it('should return valid for correct data', () => {
      const data: ProfileUpdateRequest = {
        first_name: 'John',
        last_name: 'Doe',
        phone: '+**********',
        bio: 'A short bio',
        date_of_birth: '1990-01-01',
      };

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: true,
        errors: [],
      });
    });

    it('should validate phone number format', () => {
      const data: ProfileUpdateRequest = {
        phone: '**********', // Missing country code
      };

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: ['Phone number must include country code (e.g., +**********)'],
      });
    });

    it('should validate bio length', () => {
      const data: ProfileUpdateRequest = {
        bio: 'A'.repeat(501), // Exceeds 500 character limit
      };

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: ['Bio cannot exceed 500 characters'],
      });
    });

    it('should validate date of birth format', () => {
      const data: ProfileUpdateRequest = {
        date_of_birth: '01/01/1990', // Wrong format
      };

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: ['Date of birth must be in YYYY-MM-DD format'],
      });
    });

    it('should return multiple validation errors', () => {
      const data: ProfileUpdateRequest = {
        phone: '**********',
        bio: 'A'.repeat(501),
        date_of_birth: '01/01/1990',
      };

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: [
          'Phone number must include country code (e.g., +**********)',
          'Bio cannot exceed 500 characters',
          'Date of birth must be in YYYY-MM-DD format',
        ],
      });
    });

    it('should handle empty data', () => {
      const data: ProfileUpdateRequest = {};

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: true,
        errors: [],
      });
    });

    it('should allow valid phone number with country code', () => {
      const data: ProfileUpdateRequest = {
        phone: '******-123-4567',
      };

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: true,
        errors: [],
      });
    });

    it('should allow bio at exactly 500 characters', () => {
      const data: ProfileUpdateRequest = {
        bio: 'A'.repeat(500),
      };

      const result = profileAPI.validateProfileData(data);

      expect(result).toEqual({
        isValid: true,
        errors: [],
      });
    });

    it('should allow valid date formats', () => {
      const validDates = ['1990-01-01', '2000-12-31', '1985-06-15'];

      validDates.forEach(date => {
        const data: ProfileUpdateRequest = {
          date_of_birth: date,
        };

        const result = profileAPI.validateProfileData(data);

        expect(result.isValid).toBe(true);
        expect(result.errors).toEqual([]);
      });
    });

    it('should reject invalid date formats', () => {
      const invalidDates = ['90-01-01', '1990-1-1', '1990/01/01', 'January 1, 1990'];

      invalidDates.forEach(date => {
        const data: ProfileUpdateRequest = {
          date_of_birth: date,
        };

        const result = profileAPI.validateProfileData(data);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Date of birth must be in YYYY-MM-DD format');
      });
    });
  });

  describe('validateBusinessProfileData', () => {
    it('should validate website URL format', () => {
      const data = { website: 'invalid-url' };
      const result = profileAPI.validateBusinessProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: ['Website must be a valid URL starting with http:// or https://'],
      });
    });

    it('should validate years of experience range', () => {
      const data = { years_of_experience: 51 };
      const result = profileAPI.validateBusinessProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: ['Years of experience must be between 0 and 50'],
      });
    });

    it('should validate social media URLs', () => {
      const data = {
        instagram: 'invalid-instagram',
        facebook: 'invalid-facebook'
      };
      const result = profileAPI.validateBusinessProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: [
          'Instagram must be a valid Instagram URL',
          'Facebook must be a valid Facebook URL'
        ],
      });
    });

    it('should validate business description length', () => {
      const data = { business_description: 'A'.repeat(1001) };
      const result = profileAPI.validateBusinessProfileData(data);

      expect(result).toEqual({
        isValid: false,
        errors: ['Business description cannot exceed 1000 characters'],
      });
    });

    it('should return valid for correct business data', () => {
      const data = {
        website: 'https://example.com',
        years_of_experience: 5,
        instagram: 'https://instagram.com/business',
        facebook: 'https://facebook.com/business',
        business_description: 'A valid business description'
      };
      const result = profileAPI.validateBusinessProfileData(data);

      expect(result).toEqual({
        isValid: true,
        errors: [],
      });
    });
  });

  describe('calculateProfileCompletion', () => {
    it('should calculate completion for customer profile', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        phone: '+**********',
        bio: 'Test bio',
        date_of_birth: '1990-01-01',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const profile: UserProfile = {
        address: '123 Main St',
        city: 'New York',
        state: 'NY',
        zip_code: '10001',
        country: 'USA',
        search_radius: 25,
        auto_accept_bookings: false,
        show_phone_publicly: false,
        show_email_publicly: true,
        allow_reviews: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const completion = profileAPI.calculateProfileCompletion(user, profile);
      expect(completion).toBe(100); // All fields completed
    });

    it('should calculate completion for provider profile', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        phone: '+**********',
        bio: 'Test bio',
        date_of_birth: '1990-01-01',
        role: 'service_provider',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const profile: UserProfile = {
        address: '123 Main St',
        city: 'New York',
        state: 'NY',
        zip_code: '10001',
        country: 'USA',
        business_name: 'Test Business',
        business_description: 'A test business',
        years_of_experience: 5,
        search_radius: 25,
        auto_accept_bookings: false,
        show_phone_publicly: false,
        show_email_publicly: true,
        allow_reviews: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const completion = profileAPI.calculateProfileCompletion(user, profile);
      expect(completion).toBe(100); // All fields completed
    });
  });

  describe('formatDisplayName', () => {
    it('should use full_name when available', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Smith Doe',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.formatDisplayName(user);
      expect(result).toBe('John Smith Doe');
    });

    it('should combine first and last name when full_name is empty', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: '',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.formatDisplayName(user);
      expect(result).toBe('John Doe');
    });

    it('should fallback to username when no names available', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: '',
        last_name: '',
        full_name: '',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.formatDisplayName(user);
      expect(result).toBe('testuser');
    });
  });

  describe('getUserInitials', () => {
    it('should return initials from first and last name', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.getUserInitials(user);
      expect(result).toBe('JD');
    });

    it('should return first two characters for single name', () => {
      const user: User = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: '',
        full_name: 'John',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.getUserInitials(user);
      expect(result).toBe('JO');
    });

    it('should return UU for unknown user', () => {
      const user: User = {
        id: '1',
        email: '',
        username: '',
        first_name: '',
        last_name: '',
        full_name: '',
        role: 'customer',
        account_status: 'active',
        is_verified: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      const result = profileAPI.getUserInitials(user);
      expect(result).toBe('UU'); // "Unknown User" -> "UU"
    });
  });
});
