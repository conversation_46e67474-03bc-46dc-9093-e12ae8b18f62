/**
 * ProfileDisplay Component Tests
 * Comprehensive test suite for profile display component
 */

import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { ProfileDisplay } from '../../../components/profile/ProfileDisplay';
import { User, UserProfile } from '../../../services/api/profile';

// Mock UI components
jest.mock('../../../components/ui/AnimatedCard', () => ({
  AnimatedCard: ({ children, ...props }: any) => <div {...props}>{children}</div>,
}));

jest.mock('../../../components/ui/FadeTransition', () => ({
  FadeTransition: ({ children, visible, ...props }: any) => 
    visible ? <div {...props}>{children}</div> : null,
}));

describe('ProfileDisplay', () => {
  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    username: 'testuser',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    full_name: '<PERSON>',
    phone: '+**********',
    role: 'customer',
    avatar: 'https://example.com/avatar.jpg',
    date_of_birth: '1990-01-01',
    bio: 'Test bio',
    account_status: 'active',
    is_verified: true,
    email_verified_at: '2023-01-01T00:00:00Z',
    phone_verified_at: '2023-01-01T00:00:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  const mockProfile: UserProfile = {
    address: '123 Main St',
    city: 'New York',
    state: 'NY',
    zip_code: '10001',
    country: 'USA',
    business_name: 'Test Business',
    business_description: 'A test business',
    years_of_experience: 5,
    website: 'https://testbusiness.com',
    search_radius: 25,
    auto_accept_bookings: true,
    show_phone_publicly: false,
    show_email_publicly: true,
    allow_reviews: true,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  describe('Basic Information Display', () => {
    it('should display user basic information correctly', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      expect(screen.getByText('John Doe')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
      expect(screen.getByText('+**********')).toBeTruthy();
      expect(screen.getByText('Test bio')).toBeTruthy();
    });

    it('should display date of birth when available', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      expect(screen.getByText('1990-01-01')).toBeTruthy();
    });

    it('should handle missing optional fields gracefully', () => {
      const userWithoutOptionalFields = {
        ...mockUser,
        bio: undefined,
        date_of_birth: undefined,
        phone: undefined,
      };

      render(<ProfileDisplay user={userWithoutOptionalFields} profile={mockProfile} />);

      expect(screen.getByText('John Doe')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
    });

    it('should display fallback name when full_name is empty', () => {
      const userWithoutFullName = {
        ...mockUser,
        full_name: '',
      };

      render(<ProfileDisplay user={userWithoutFullName} profile={mockProfile} />);

      expect(screen.getByText('John Doe')).toBeTruthy(); // Should construct from first + last
    });

    it('should display username when no names available', () => {
      const userWithOnlyUsername = {
        ...mockUser,
        full_name: '',
        first_name: '',
        last_name: '',
      };

      render(<ProfileDisplay user={userWithOnlyUsername} profile={mockProfile} />);

      expect(screen.getByText('testuser')).toBeTruthy();
    });
  });

  describe('Role-Specific Display', () => {
    it('should display customer role correctly', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      // Should not display business information for customers
      expect(screen.queryByText('Test Business')).toBeFalsy();
      expect(screen.queryByText('A test business')).toBeFalsy();
    });

    it('should display provider role and business information', () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };
      
      render(<ProfileDisplay user={providerUser} profile={mockProfile} />);

      expect(screen.getByText('Test Business')).toBeTruthy();
      expect(screen.getByText('A test business')).toBeTruthy();
      expect(screen.getByText('5')).toBeTruthy(); // years of experience
      expect(screen.getByText('https://testbusiness.com')).toBeTruthy();
    });

    it('should handle provider without business information', () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };
      const profileWithoutBusiness = {
        ...mockProfile,
        business_name: undefined,
        business_description: undefined,
        years_of_experience: undefined,
        website: undefined,
      };

      render(<ProfileDisplay user={providerUser} profile={profileWithoutBusiness} />);

      expect(screen.getByText('John Doe')).toBeTruthy();
      // Should still render business section but with empty/default values
    });
  });

  describe('Contact Information Display', () => {
    it('should display contact information correctly', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      expect(screen.getByText('<EMAIL>')).toBeTruthy();
      expect(screen.getByText('+**********')).toBeTruthy();
    });

    it('should handle missing contact information', () => {
      const userWithoutContact = {
        ...mockUser,
        phone: undefined,
      };

      render(<ProfileDisplay user={userWithoutContact} profile={mockProfile} />);

      expect(screen.getByText('<EMAIL>')).toBeTruthy();
      // Phone should not be displayed when missing
    });
  });

  describe('Location Information Display', () => {
    it('should display location information when available', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      expect(screen.getByText('123 Main St')).toBeTruthy();
      expect(screen.getByText('New York')).toBeTruthy();
      expect(screen.getByText('NY')).toBeTruthy();
      expect(screen.getByText('10001')).toBeTruthy();
      expect(screen.getByText('USA')).toBeTruthy();
    });

    it('should handle missing location information', () => {
      const profileWithoutLocation = {
        ...mockProfile,
        address: undefined,
        city: undefined,
        state: undefined,
        zip_code: undefined,
        country: undefined,
      };

      render(<ProfileDisplay user={mockUser} profile={profileWithoutLocation} />);

      expect(screen.getByText('John Doe')).toBeTruthy();
      // Location section should not be displayed when all fields are missing
    });

    it('should display partial location information', () => {
      const profileWithPartialLocation = {
        ...mockProfile,
        address: undefined,
        zip_code: undefined,
      };

      render(<ProfileDisplay user={mockUser} profile={profileWithPartialLocation} />);

      expect(screen.getByText('New York')).toBeTruthy();
      expect(screen.getByText('NY')).toBeTruthy();
      expect(screen.getByText('USA')).toBeTruthy();
    });
  });

  describe('Preferences Display', () => {
    it('should display preferences correctly', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      // Search radius should be displayed
      expect(screen.getByText('25')).toBeTruthy();
    });

    it('should display privacy settings for providers', () => {
      const providerUser = { ...mockUser, role: 'service_provider' as const };
      
      render(<ProfileDisplay user={providerUser} profile={mockProfile} />);

      // Privacy settings should be visible for providers
      // These might be displayed as toggle states or text
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      const profileDisplay = screen.getByTestId('profile-display');
      expect(profileDisplay).toBeTruthy();
    });

    it('should have proper semantic structure', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      // Should have proper heading hierarchy and semantic elements
      const profileDisplay = screen.getByTestId('profile-display');
      expect(profileDisplay.props.accessibilityRole).toBe('region');
    });
  });

  describe('Error Handling', () => {
    it('should handle null user gracefully', () => {
      render(<ProfileDisplay user={null} profile={mockProfile} />);

      // Should not crash and should display appropriate fallback
      expect(screen.getByTestId('profile-display')).toBeTruthy();
    });

    it('should handle null profile gracefully', () => {
      render(<ProfileDisplay user={mockUser} profile={null} />);

      // Should still display user information
      expect(screen.getByText('John Doe')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
    });

    it('should handle both null user and profile', () => {
      render(<ProfileDisplay user={null} profile={null} />);

      // Should not crash
      expect(screen.getByTestId('profile-display')).toBeTruthy();
    });
  });

  describe('Visual Design Compliance', () => {
    it('should use proper styling structure', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      const profileDisplay = screen.getByTestId('profile-display');
      expect(profileDisplay).toBeTruthy();
      
      // Should use AnimatedCard components for sections
      // Should follow atomic design principles
    });

    it('should be responsive', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      // Should render without layout issues
      expect(screen.getByTestId('profile-display')).toBeTruthy();
    });
  });
});
