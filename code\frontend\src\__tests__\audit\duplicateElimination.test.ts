/**
 * Duplicate Elimination Verification Test Suite
 * Tests to verify Rule R-003 compliance and elimination of duplicate components/services
 * 
 * EPIC-AUDIT-002: Eliminate Duplicate Components and Services Violating Rule R-003
 * Task: TEST-01
 */

import fs from 'fs';
import path from 'path';

describe('EPIC-AUDIT-002: Duplicate Elimination Verification', () => {
  const projectRoot = path.resolve(__dirname, '../../../../..');
  const currentCodePath = path.join(projectRoot, 'code');
  const referenceCodePath = path.join(projectRoot, 'reference-code');

  describe('CRITICAL: Enhanced Component Elimination', () => {
    it('should NOT have EnhancedUserExperience component in reference-code', () => {
      const enhancedUXPath = path.join(
        referenceCodePath,
        'frontend_v1/src/components/ux/EnhancedUserExperience.tsx'
      );
      
      // This test will FAIL until CODE-01 is implemented
      expect(fs.existsSync(enhancedUXPath)).toBe(false);
    });

    it('should NOT have EnhancedVisualDesignSystem component in reference-code', () => {
      const enhancedVDSPath = path.join(
        referenceCodePath,
        'frontend_v1/src/components/design-system/EnhancedVisualDesignSystem.tsx'
      );
      
      // This test will FAIL until CODE-01 is implemented
      expect(fs.existsSync(enhancedVDSPath)).toBe(false);
    });

    it('should NOT have any files with "Enhanced" prefix in current codebase', () => {
      const findEnhancedFiles = (dir: string): string[] => {
        const enhancedFiles: string[] = [];
        
        if (!fs.existsSync(dir)) return enhancedFiles;
        
        const files = fs.readdirSync(dir, { withFileTypes: true });
        
        for (const file of files) {
          const fullPath = path.join(dir, file.name);
          
          if (file.isDirectory()) {
            enhancedFiles.push(...findEnhancedFiles(fullPath));
          } else if (file.name.startsWith('Enhanced') && file.name.endsWith('.tsx')) {
            enhancedFiles.push(fullPath);
          }
        }
        
        return enhancedFiles;
      };

      const enhancedFiles = findEnhancedFiles(currentCodePath);
      
      // Should have no Enhanced components in current codebase
      expect(enhancedFiles).toHaveLength(0);
      
      if (enhancedFiles.length > 0) {
        console.warn('Found Enhanced components that violate Rule R-003:', enhancedFiles);
      }
    });
  });

  describe('CRITICAL: Theme System Duplicate Elimination', () => {
    it('should NOT have themeOverride.ts in reference-code', () => {
      const themeOverridePath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/themeOverride.ts'
      );
      
      // This test will FAIL until CODE-02 is implemented
      expect(fs.existsSync(themeOverridePath)).toBe(false);
    });

    it('should NOT have comprehensiveThemeFix.ts in reference-code', () => {
      const comprehensiveThemeFixPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/comprehensiveThemeFix.ts'
      );
      
      // This test will FAIL until CODE-02 is implemented
      expect(fs.existsSync(comprehensiveThemeFixPath)).toBe(false);
    });

    it('should NOT have themeInitializer.ts in reference-code', () => {
      const themeInitializerPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/themeInitializer.ts'
      );
      
      // This test will FAIL until CODE-02 is implemented
      expect(fs.existsSync(themeInitializerPath)).toBe(false);
    });

    it('should NOT have globalThemeSafety.ts in reference-code', () => {
      const globalThemeSafetyPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/globalThemeSafety.ts'
      );
      
      // This test will FAIL until CODE-02 is implemented
      expect(fs.existsSync(globalThemeSafetyPath)).toBe(false);
    });

    it('should have single authoritative theme system in current code', () => {
      const currentThemePath = path.join(
        currentCodePath,
        'frontend/src/theme/index.ts'
      );

      // Should have our authoritative theme system
      expect(fs.existsSync(currentThemePath)).toBe(true);

      // Verify it contains our Vierla color palette
      const themeContent = fs.readFileSync(currentThemePath, 'utf8');
      expect(themeContent).toContain('#F4F1E8'); // Warm Cream primary background
      expect(themeContent).toContain('#364035'); // Forest Green primary
    });
  });

  describe('HIGH: Error Handling Service Consolidation', () => {
    it('should NOT have duplicate errorHandler.ts in reference-code', () => {
      const referenceErrorHandlerPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/errorHandler.ts'
      );
      
      // This test will FAIL until CODE-03 is implemented
      expect(fs.existsSync(referenceErrorHandlerPath)).toBe(false);
    });

    it('should NOT have errorHandlingService.ts in reference-code', () => {
      const errorHandlingServicePath = path.join(
        referenceCodePath,
        'frontend_v1/src/services/errorHandlingService.ts'
      );
      
      // This test will FAIL until CODE-03 is implemented
      expect(fs.existsSync(errorHandlingServicePath)).toBe(false);
    });

    it('should NOT have runtimeErrorHandler.ts in reference-code', () => {
      const runtimeErrorHandlerPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/runtimeErrorHandler.ts'
      );
      
      // This test will FAIL until CODE-03 is implemented
      expect(fs.existsSync(runtimeErrorHandlerPath)).toBe(false);
    });

    it('should NOT have globalErrorInterceptor.ts in reference-code', () => {
      const globalErrorInterceptorPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/globalErrorInterceptor.ts'
      );
      
      // This test will FAIL until CODE-03 is implemented
      expect(fs.existsSync(globalErrorInterceptorPath)).toBe(false);
    });

    it('should have single authoritative error handler in current code', () => {
      const currentErrorHandlerPath = path.join(
        currentCodePath,
        'frontend/src/utils/errorHandler.ts'
      );

      // Should have our primary error handler
      expect(fs.existsSync(currentErrorHandlerPath)).toBe(true);
    });
  });

  describe('MEDIUM: Authentication Service Consolidation', () => {
    it('should NOT have authServiceEpic01.ts duplicate in current code', () => {
      const authServiceEpic01Path = path.join(
        currentCodePath,
        'frontend/src/services/authServiceEpic01.ts'
      );
      
      // This test will FAIL until CODE-04 is implemented
      expect(fs.existsSync(authServiceEpic01Path)).toBe(false);
    });

    it('should have single authoritative authService.ts in current code', () => {
      const authServicePath = path.join(
        currentCodePath,
        'frontend/src/services/authService.ts'
      );

      // Should have our primary auth service
      expect(fs.existsSync(authServicePath)).toBe(true);
    });

    it('should NOT have useEnhancedAuth hook (should be renamed)', () => {
      const useEnhancedAuthPath = path.join(
        currentCodePath,
        'frontend/src/hooks/useEnhancedAuth.ts'
      );
      
      // This test will FAIL until CODE-04 is implemented (hook should be renamed)
      expect(fs.existsSync(useEnhancedAuthPath)).toBe(false);
    });

    it('should have properly named auth hook (not Enhanced)', () => {
      const useAuthPath = path.join(
        currentCodePath,
        'frontend/src/hooks/useAuth.ts'
      );

      // Should have renamed auth hook
      expect(fs.existsSync(useAuthPath)).toBe(true);
    });
  });

  describe('MEDIUM: Theme Utility Duplicate Elimination', () => {
    it('should NOT have contrastEnhancer.ts in reference-code', () => {
      const contrastEnhancerPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/contrastEnhancer.ts'
      );
      
      // This test will FAIL until CODE-02 is implemented
      expect(fs.existsSync(contrastEnhancerPath)).toBe(false);
    });

    it('should NOT have useSafeThemeWithMediumFix.ts in reference-code', () => {
      const useSafeThemePath = path.join(
        referenceCodePath,
        'frontend_v1/src/hooks/useSafeThemeWithMediumFix.ts'
      );
      
      // This test will FAIL until CODE-02 is implemented
      expect(fs.existsSync(useSafeThemePath)).toBe(false);
    });

    it('should NOT have runtimeMediumPropertyFix.ts in reference-code', () => {
      const runtimeMediumFixPath = path.join(
        referenceCodePath,
        'frontend_v1/src/utils/runtimeMediumPropertyFix.ts'
      );
      
      // This test will FAIL until CODE-02 is implemented
      expect(fs.existsSync(runtimeMediumFixPath)).toBe(false);
    });
  });

  describe('Rule R-003 Compliance Validation', () => {
    it('should NOT have any files with "Enhanced" in the name', () => {
      const findFilesWithPattern = (dir: string, pattern: RegExp): string[] => {
        const matchingFiles: string[] = [];

        if (!fs.existsSync(dir)) return matchingFiles;

        const files = fs.readdirSync(dir, { withFileTypes: true });

        for (const file of files) {
          const fullPath = path.join(dir, file.name);

          // Skip node_modules, coverage, archived, and reference directories
          if (file.isDirectory() &&
              (file.name === 'node_modules' ||
               file.name === 'coverage' ||
               file.name === '__pycache__' ||
               file.name === 'venv' ||
               file.name === 'archived-onboarding' ||
               file.name === 'reference-code')) {
            continue;
          }

          if (file.isDirectory()) {
            matchingFiles.push(...findFilesWithPattern(fullPath, pattern));
          } else if (pattern.test(file.name)) {
            // Skip coverage HTML files and other generated files
            if (!fullPath.includes('coverage') &&
                !fullPath.includes('node_modules') &&
                !fullPath.includes('.html')) {
              matchingFiles.push(fullPath);
            }
          }
        }

        return matchingFiles;
      };

      const enhancedFiles = findFilesWithPattern(projectRoot, /Enhanced/i);

      // Should have no files with "Enhanced" in the name (excluding external dependencies)
      expect(enhancedFiles).toHaveLength(0);

      if (enhancedFiles.length > 0) {
        console.warn('Found files with "Enhanced" pattern that violate Rule R-003:', enhancedFiles);
      }
    });

    it('should NOT have any files with "Consolidated" in the name', () => {
      const findFilesWithPattern = (dir: string, pattern: RegExp): string[] => {
        const matchingFiles: string[] = [];
        
        if (!fs.existsSync(dir)) return matchingFiles;
        
        const files = fs.readdirSync(dir, { withFileTypes: true });
        
        for (const file of files) {
          const fullPath = path.join(dir, file.name);
          
          if (file.isDirectory()) {
            matchingFiles.push(...findFilesWithPattern(fullPath, pattern));
          } else if (pattern.test(file.name)) {
            matchingFiles.push(fullPath);
          }
        }
        
        return matchingFiles;
      };

      const consolidatedFiles = findFilesWithPattern(projectRoot, /Consolidated/i);
      
      // Should have no files with "Consolidated" in the name
      expect(consolidatedFiles).toHaveLength(0);
      
      if (consolidatedFiles.length > 0) {
        console.warn('Found files with "Consolidated" pattern that violate Rule R-003:', consolidatedFiles);
      }
    });

    it('should have clean, non-duplicate service structure', () => {
      // Verify single source of truth for each service type
      const servicesDir = path.join(currentCodePath, 'frontend/src/services');
      
      if (fs.existsSync(servicesDir)) {
        const serviceFiles = fs.readdirSync(servicesDir);
        
        // Should not have multiple auth services
        const authServices = serviceFiles.filter(file => 
          file.includes('auth') && file.endsWith('.ts')
        );
        
        expect(authServices).toHaveLength(1);
        expect(authServices[0]).toBe('authService.ts');
      }
    });
  });

  describe('Functionality Preservation Validation', () => {
    it('should maintain all essential service exports after consolidation', () => {
      // Test that consolidated services maintain their essential exports
      const authServicePath = path.join(
        currentCodePath,
        'frontend/src/services/authService.ts'
      );

      if (fs.existsSync(authServicePath)) {
        const authServiceContent = fs.readFileSync(authServicePath, 'utf8');

        // Should export auth service and have login/logout methods
        expect(authServiceContent).toMatch(/export.*authService/);
        expect(authServiceContent).toMatch(/login.*\(/);
        expect(authServiceContent).toMatch(/logout.*\(/);
      }
    });

    it('should maintain error handling functionality after consolidation', () => {
      const errorHandlerPath = path.join(
        currentCodePath,
        'frontend/src/utils/errorHandler.ts'
      );

      if (fs.existsSync(errorHandlerPath)) {
        const errorHandlerContent = fs.readFileSync(errorHandlerPath, 'utf8');

        // Should export error handling functions
        expect(errorHandlerContent).toMatch(/export.*parseApiError/);
        expect(errorHandlerContent).toMatch(/export.*EnhancedError/);
      }
    });
  });
});
