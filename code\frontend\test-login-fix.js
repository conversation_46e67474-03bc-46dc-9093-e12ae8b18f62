/**
 * Test script to verify login navigation fix
 * This script tests the login flow and navigation
 */

const axios = require('axios');

const API_BASE_URL = 'http://************:8000/api';

async function testLoginFlow() {
  console.log('🧪 Testing Login Navigation Fix...\n');
  
  try {
    // Test 1: Check if backend is accessible
    console.log('1. Testing backend connectivity...');
    const healthCheck = await axios.get(`${API_BASE_URL}/auth/login/`, {
      timeout: 5000,
      validateStatus: () => true // Accept any status code
    });
    
    if (healthCheck.status === 405) {
      console.log('✅ Backend is accessible (405 Method Not Allowed is expected for GET on login endpoint)');
    } else {
      console.log(`⚠️  Backend responded with status: ${healthCheck.status}`);
    }
    
    // Test 2: Test actual login
    console.log('\n2. Testing login API call...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login/`, {
      email: '<EMAIL>',
      password: 'VierlaTest123!'
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (loginResponse.status === 200) {
      console.log('✅ Login API call successful!');
      console.log('📋 Response data:', {
        hasAccess: !!loginResponse.data.access,
        hasRefresh: !!loginResponse.data.refresh,
        hasUser: !!loginResponse.data.user,
        userRole: loginResponse.data.user?.role,
        userEmail: loginResponse.data.user?.email
      });
      
      // Test 3: Verify token format
      if (loginResponse.data.access && loginResponse.data.refresh) {
        console.log('\n3. Token validation...');
        console.log('✅ Access token received');
        console.log('✅ Refresh token received');
        console.log('✅ User data received');
        
        console.log('\n🎉 LOGIN FLOW TEST PASSED!');
        console.log('\n📝 Summary:');
        console.log('- Backend is accessible from frontend');
        console.log('- Login API returns valid tokens');
        console.log('- User data is properly formatted');
        console.log('- Navigation refresh should work with these tokens');
        
      } else {
        console.log('\n❌ Missing tokens in response');
      }
      
    } else {
      console.log(`❌ Login failed with status: ${loginResponse.status}`);
    }
    
  } catch (error) {
    console.log('\n❌ Test failed with error:');
    if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Connection refused - backend may not be running or not accessible');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('⏰ Connection timeout - backend may be slow or unreachable');
    } else {
      console.log('📋 Error details:', {
        code: error.code,
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
    }
  }
}

// Run the test
testLoginFlow();
