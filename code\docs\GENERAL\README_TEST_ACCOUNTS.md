# Test Accounts Documentation

## 📍 Single Source of Truth

**All test account information is now consolidated in:**
👉 **[CONSOLIDATED_TEST_ACCOUNTS.md](./CONSOLIDATED_TEST_ACCOUNTS.md)**

## 🚀 Quick Access

### Working Test Accounts
- **Customer:** `<EMAIL>` / `TestPass123!`
- **Provider:** `<EMAIL>` / `TestPass123!`

### Backend API
- **Base URL:** `http://localhost:8000/api/`
- **Admin Panel:** `http://localhost:8000/admin/`

## 📋 Documentation Structure

### Current Files
- ✅ `CONSOLIDATED_TEST_ACCOUNTS.md` - **Primary documentation** (use this)
- ✅ `backend/docs/TEST_ACCOUNTS_README.md` - System documentation
- ✅ `README_TEST_ACCOUNTS.md` - This reference file

### Removed Files (Duplicates)
- ❌ `TEST_CREDENTIALS_CONSOLIDATED.md` - Merged into consolidated file
- ❌ `TEST-ACCOUNTS-SPECIFICATION.md` - Contained non-working accounts

## 🔄 Migration Status

### Database
- **Current:** SQLite (working)
- **Target:** PostgreSQL (configured, pending user setup)

### Test Accounts
- **Status:** All working accounts verified
- **Compatibility:** Ready for PostgreSQL migration

## 📞 Support

If you need test account access:
1. Check `CONSOLIDATED_TEST_ACCOUNTS.md` first
2. Verify backend is running on `http://localhost:8000`
3. Use the verified working accounts listed above

---

**Last Updated:** August 6, 2025  
**Maintained by:** Vierla Development Team
