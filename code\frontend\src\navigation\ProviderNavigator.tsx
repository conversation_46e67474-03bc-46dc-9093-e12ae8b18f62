/**
 * Provider Navigator
 * Navigation structure for service providers
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

import { ProviderDashboardScreen } from '../screens/provider/ProviderDashboardScreen';
import { ProviderServicesScreen } from '../screens/provider/ProviderServicesScreen';
import { AddServiceScreen } from '../screens/provider/AddServiceScreen';
import { EditServiceScreen } from '../screens/provider/EditServiceScreen';
import { ProfileScreen } from '../screens/main/ProfileScreen';
import { colors } from '../theme';
import { TabBarIcon } from '../components/navigation/TabBarIcon';

export type ProviderTabParamList = {
  Dashboard: undefined;
  Services: undefined;
  Bookings: undefined;
  Profile: undefined;
};

export type ProviderStackParamList = {
  ProviderTabs: undefined;
  AddService: undefined;
  EditService: { serviceId: string };
  ServiceDetails: { serviceId: string };
  ProviderAnalytics: undefined;
  ProviderBookings: undefined;
};

const Tab = createBottomTabNavigator<ProviderTabParamList>();
const Stack = createStackNavigator<ProviderStackParamList>();

// Placeholder components for missing screens
const ProviderBookingsScreen: React.FC = () => {
  const React = require('react');
  const { View, Text, StyleSheet } = require('react-native');
  
  return (
    <View style={styles.placeholder}>
      <Text style={styles.placeholderText}>Provider Bookings</Text>
      <Text style={styles.placeholderSubtext}>Coming Soon</Text>
    </View>
  );
};

const ProviderTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;
          let showBadge = false;
          let badgeCount = 0;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Services') {
            iconName = focused ? 'grid' : 'grid-outline';
            // Example: Show badge for pending service approvals
            showBadge = true;
            badgeCount = 1; // This would come from your service state
          } else if (route.name === 'Bookings') {
            iconName = focused ? 'calendar' : 'calendar-outline';
            // Example: Show badge for new bookings
            showBadge = true;
            badgeCount = 3; // This would come from your booking state
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
            // Example: Show dot for profile completion or verification
            showBadge = true; // This would be conditional based on profile status
          } else {
            iconName = 'help-outline';
          }

          return (
            <TabBarIcon
              name={iconName}
              focused={focused}
              size={size}
              color={color}
              showBadge={showBadge}
              badgeCount={badgeCount}
              testID={`provider-tab-icon-${route.name.toLowerCase()}`}
            />
          );
        },
        tabBarActiveTintColor: colors.primary, // Forest Green (#364035) for active
        tabBarInactiveTintColor: colors.secondary, // Sage Green (#8B9A8C) for inactive
        tabBarStyle: {
          backgroundColor: colors.background.secondary, // Warm Cream background
          borderTopColor: colors.extended.sageVariant, // Subtle border
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: 4,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={ProviderDashboardScreen}
        options={{
          title: 'Dashboard',
        }}
      />
      <Tab.Screen
        name="Services"
        component={ProviderServicesScreen}
        options={{
          title: 'Services',
        }}
      />
      <Tab.Screen 
        name="Bookings" 
        component={ProviderBookingsScreen}
        options={{
          title: 'Bookings',
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          title: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};

export const ProviderNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: colors.background },
      }}
    >
      <Stack.Screen 
        name="ProviderTabs" 
        component={ProviderTabs}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="AddService"
        component={AddServiceScreen}
        options={{
          headerShown: true,
          title: 'Add Service',
          headerStyle: {
            backgroundColor: colors.white,
          },
          headerTintColor: colors.textPrimary,
          headerTitleStyle: {
            fontWeight: '600',
          },
        }}
      />

      <Stack.Screen 
        name="EditService" 
        component={EditServiceScreen}
        options={{
          headerShown: true,
          title: 'Edit Service',
          headerStyle: {
            backgroundColor: colors.white,
          },
          headerTintColor: colors.textPrimary,
          headerTitleStyle: {
            fontWeight: '600',
          },
        }}
      />
    </Stack.Navigator>
  );
};

const styles = require('react-native').StyleSheet.create({
  placeholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: 20,
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 16,
    color: colors.textSecondary,
  },
});
