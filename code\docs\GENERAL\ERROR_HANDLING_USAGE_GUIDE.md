# Error Handling System - Usage Guide

**Version:** 1.0.0  
**Date:** August 6, 2025  
**Status:** Implementation Complete

## Overview

This guide provides comprehensive instructions for using the standardized error handling system in the Vierla application. The system provides consistent error display, user feedback, and recovery mechanisms across the entire application.

## Quick Start

### 1. Basic Setup

First, wrap your app with the ToastProvider:

```typescript
import { ToastProvider } from './src/components/error';

export default function App() {
  return (
    <ToastProvider>
      {/* Your app content */}
    </ToastProvider>
  );
}
```

### 2. Using <PERSON>rror Handler Hook

```typescript
import { useErrorHandler } from './src/components/error';

function MyComponent() {
  const { handleError, handleNetworkError, showErrorAlert } = useErrorHandler();

  const handleApiCall = async () => {
    try {
      const response = await fetch('/api/data');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      handleNetworkError(error as Error, {
        screen: 'MyComponent',
        action: 'fetchData',
      });
    }
  };

  return (
    // Your component JSX
  );
}
```

### 3. Using Toast Notifications

```typescript
import { useToast } from './src/components/error';

function MyComponent() {
  const { showSuccess, showError, showWarning } = useToast();

  const handleSuccess = () => {
    showSuccess('Success!', 'Operation completed successfully');
  };

  const handleError = () => {
    showError('Error', 'Something went wrong', {
      persistent: true,
      action: {
        label: 'Retry',
        onPress: () => console.log('Retry pressed'),
      },
    });
  };

  return (
    // Your component JSX
  );
}
```

## Component Usage

### ErrorDisplay Component

Display errors inline within your components:

```typescript
import { ErrorDisplay, ErrorSeverity, ErrorVariant } from './src/components/error';

function FormComponent() {
  const [error, setError] = useState<string | null>(null);

  return (
    <View>
      {error && (
        <ErrorDisplay
          error={error}
          severity={ErrorSeverity.MEDIUM}
          variant={ErrorVariant.INLINE}
          dismissible
          onDismiss={() => setError(null)}
          onRetry={() => {
            setError(null);
            // Retry logic
          }}
        />
      )}
      {/* Your form content */}
    </View>
  );
}
```

### ValidationError Component

Display form validation errors:

```typescript
import { ValidationError } from './src/components/error';

function LoginForm() {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  return (
    <View>
      <TextInput placeholder="Email" />
      <ValidationError
        errors={validationErrors}
        field="email"
      />
      {/* Other form fields */}
    </View>
  );
}
```

### ErrorBoundary Component

Catch and handle React errors:

```typescript
import { ErrorBoundary } from './src/components/error';

function App() {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.log('Error caught by boundary:', error);
      }}
      enableRetry={true}
      enableReporting={true}
    >
      <MyComponent />
    </ErrorBoundary>
  );
}
```

## Advanced Usage

### Custom Error Types

Create custom error types for specific use cases:

```typescript
import { createAppError, ErrorType, ErrorSeverity } from './src/components/error';

const customError = createAppError(
  'Custom error message',
  ErrorType.VALIDATION,
  ErrorSeverity.LOW,
  {
    screen: 'CustomScreen',
    component: 'CustomComponent',
    metadata: { customData: 'value' },
  }
);
```

### Network Error Handling

Handle API errors with automatic retry:

```typescript
import { useErrorHandler, withErrorHandling } from './src/components/error';

function ApiService() {
  const { handleNetworkError } = useErrorHandler();

  const fetchData = async () => {
    const { data, error } = await withErrorHandling(
      () => fetch('/api/data').then(res => res.json()),
      { screen: 'ApiService', action: 'fetchData' }
    );

    if (error) {
      handleNetworkError(error.originalError!, error.context);
      return null;
    }

    return data;
  };

  return { fetchData };
}
```

### Form Validation

Integrate with form validation:

```typescript
import { useErrorHandler } from './src/components/error';

function useFormValidation() {
  const { handleValidationError } = useErrorHandler();

  const validateEmail = (email: string) => {
    if (!email) {
      handleValidationError('email', 'Email is required');
      return false;
    }
    if (!/\S+@\S+\.\S+/.test(email)) {
      handleValidationError('email', 'Please enter a valid email');
      return false;
    }
    return true;
  };

  return { validateEmail };
}
```

## Configuration Options

### Error Handler Configuration

```typescript
import { useErrorHandler } from './src/components/error';

const errorHandler = useErrorHandler({
  enableLogging: true,
  enableHaptics: true,
  enableToasts: true,
  enableModals: true,
  maxRetries: 3,
  retryDelay: 1000,
});
```

### Toast Configuration

```typescript
import { useToast } from './src/components/error';

const { showError } = useToast();

showError('Error Title', 'Error message', {
  duration: 5000,
  persistent: false,
  enableHaptics: true,
  action: {
    label: 'Action',
    onPress: () => console.log('Action pressed'),
  },
});
```

## Best Practices

### 1. Error Context

Always provide context when handling errors:

```typescript
const context = {
  screen: 'LoginScreen',
  component: 'LoginForm',
  action: 'submitLogin',
  userId: user?.id,
  metadata: { attempt: loginAttempts },
};

handleError(error, context);
```

### 2. Error Severity

Choose appropriate severity levels:

- **LOW**: Validation errors, warnings
- **MEDIUM**: Network errors, general failures
- **HIGH**: Authentication errors, critical operations
- **CRITICAL**: App-breaking errors, security issues

### 3. User-Friendly Messages

Provide clear, actionable error messages:

```typescript
// Good
showError('Connection Issue', 'Please check your internet connection and try again.');

// Bad
showError('Error', 'Network request failed with status 500');
```

### 4. Error Recovery

Always provide recovery options when possible:

```typescript
showError('Upload Failed', 'Your file could not be uploaded.', {
  action: {
    label: 'Try Again',
    onPress: retryUpload,
  },
});
```

## Testing

### Unit Testing

Test error handling components:

```typescript
import { render, fireEvent } from '@testing-library/react-native';
import { ErrorDisplay } from './src/components/error';

test('ErrorDisplay shows retry button for retryable errors', () => {
  const onRetry = jest.fn();
  const { getByTestId } = render(
    <ErrorDisplay
      error="Network error"
      onRetry={onRetry}
      testID="error-display"
    />
  );

  fireEvent.press(getByTestId('error-display-retry'));
  expect(onRetry).toHaveBeenCalled();
});
```

### Integration Testing

Test error flows:

```typescript
test('handles network errors correctly', async () => {
  const { result } = renderHook(() => useErrorHandler());
  
  const error = new Error('Network failed');
  const appError = result.current.handleNetworkError(error);
  
  expect(appError.type).toBe(ErrorType.NETWORK);
  expect(appError.severity).toBe(ErrorSeverity.MEDIUM);
});
```

## Troubleshooting

### Common Issues

1. **Toasts not showing**: Ensure ToastProvider is wrapping your app
2. **Haptics not working**: Check device capabilities and permissions
3. **Error boundary not catching**: Ensure it's wrapping the problematic component

### Debug Mode

In development, detailed error information is logged to console:

```typescript
// Enable debug logging
const errorHandler = useErrorHandler({
  enableLogging: true,
});
```

## Migration Guide

### From Alert.alert

Replace Alert.alert calls with the error handling system:

```typescript
// Before
Alert.alert('Error', 'Something went wrong');

// After
const { showErrorAlert } = useErrorHandler();
showErrorAlert('Error', 'Something went wrong');
```

### From Custom Error Components

Replace custom error components with standardized ones:

```typescript
// Before
<CustomErrorComponent message={error} />

// After
<ErrorDisplay error={error} variant={ErrorVariant.INLINE} />
```

---

**Note**: This system is designed to be comprehensive yet simple to use. Start with basic usage and gradually adopt advanced features as needed.
