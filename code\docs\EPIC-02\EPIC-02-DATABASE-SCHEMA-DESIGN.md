# EPIC-02: Service Browsing & Display - Database Schema Design

## Overview
This document outlines the database schema design for EPIC-02, ensuring 100% feature parity with the legacy system while optimizing for mobile-first design and performance.

## Core Models

### 1. ServiceCategory Model
**Purpose**: Organize services into hierarchical categories for easy browsing and filtering.

**Key Features**:
- UUID primary key for scalability
- Hierarchical support (parent/child relationships)
- Mobile optimization with icons and colors
- SEO-friendly slugs
- Popularity and status flags

**Fields**:
```python
class ServiceCategory(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=120, unique=True)
    description = models.TextField()
    icon = models.CharField(max_length=50)  # Icon identifier
    color = models.CharField(max_length=7, default='#8FBC8F')  # Hex color
    image = models.ImageField(upload_to='categories/%Y/%m/', blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='subcategories')
    is_popular = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    mobile_icon = models.CharField(max_length=50, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

### 2. ServiceProvider Model
**Purpose**: Store comprehensive business information for service providers.

**Key Features**:
- Links to User model for authentication
- Complete business profile information
- Geolocation support for location-based services
- Social media integration
- Verification and rating system
- Business metrics tracking

**Fields**:
```python
class ServiceProvider(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='service_provider')
    business_name = models.CharField(max_length=200)
    business_description = models.TextField()
    business_phone = models.CharField(max_length=20)
    business_email = models.EmailField()
    
    # Location Information
    address = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    zip_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100, default='Canada')
    latitude = models.DecimalField(max_digits=10, decimal_places=8, blank=True, null=True)
    longitude = models.DecimalField(max_digits=11, decimal_places=8, blank=True, null=True)
    
    # Online Presence
    website = models.URLField(blank=True)
    instagram_handle = models.CharField(max_length=100, blank=True)
    facebook_url = models.URLField(blank=True)
    
    # Media
    profile_image = models.ImageField(upload_to='providers/profiles/%Y/%m/', blank=True, null=True)
    cover_image = models.ImageField(upload_to='providers/covers/%Y/%m/', blank=True, null=True)
    
    # Business Status
    is_verified = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    
    # Categories and Ratings
    categories = models.ManyToManyField(ServiceCategory, related_name='providers')
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    review_count = models.PositiveIntegerField(default=0)
    
    # Business Metrics
    total_bookings = models.PositiveIntegerField(default=0)
    years_of_experience = models.PositiveIntegerField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

### 3. Service Model
**Purpose**: Individual services offered by providers with comprehensive details.

**Key Features**:
- Flexible pricing models (fixed, hourly, range, consultation)
- Duration and buffer time management
- Service media support
- Availability and popularity flags
- Mobile-optimized descriptions
- Booking count tracking

**Fields**:
```python
class Service(models.Model):
    PRICE_TYPE_CHOICES = [
        ('fixed', 'Fixed Price'),
        ('hourly', 'Hourly Rate'),
        ('range', 'Price Range'),
        ('consultation', 'Consultation Required'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    provider = models.ForeignKey(ServiceProvider, on_delete=models.CASCADE, related_name='services')
    category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE, related_name='services')
    
    # Service Information
    name = models.CharField(max_length=200)
    description = models.TextField()
    short_description = models.CharField(max_length=255, blank=True)
    mobile_description = models.TextField(blank=True)
    
    # Pricing Information
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    price_type = models.CharField(max_length=20, choices=PRICE_TYPE_CHOICES, default='fixed')
    max_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    
    # Duration Information
    duration = models.PositiveIntegerField()  # minutes
    buffer_time = models.PositiveIntegerField(default=15)  # minutes
    
    # Service Media
    image = models.ImageField(upload_to='services/%Y/%m/', blank=True, null=True)
    
    # Service Status
    is_popular = models.BooleanField(default=False)
    is_available = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    
    # Additional Information
    requirements = models.TextField(blank=True)
    preparation_instructions = models.TextField(blank=True)
    
    # Metrics
    booking_count = models.PositiveIntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

## Relationships

### Primary Relationships
1. **User → ServiceProvider**: One-to-One relationship
2. **ServiceProvider → Service**: One-to-Many relationship
3. **ServiceCategory → Service**: One-to-Many relationship
4. **ServiceCategory → ServiceProvider**: Many-to-Many relationship
5. **ServiceCategory → ServiceCategory**: Self-referential (parent/child)

### Database Indexes
- ServiceCategory: name, slug, is_active, is_popular
- ServiceProvider: business_name, city, is_active, is_verified, rating
- Service: name, base_price, is_active, is_available, is_popular, created_at

## API Endpoints Design

### Service Categories
- `GET /api/catalog/categories/` - List all categories
- `GET /api/catalog/categories/{id}/` - Category details
- `GET /api/catalog/categories/{id}/services/` - Services in category

### Service Providers
- `GET /api/catalog/providers/` - List providers with filtering
- `GET /api/catalog/providers/{id}/` - Provider details
- `GET /api/catalog/providers/{id}/services/` - Provider's services

### Services
- `GET /api/catalog/services/` - List services with filtering and search
- `GET /api/catalog/services/{id}/` - Service details
- `GET /api/catalog/search/services/` - Enhanced search endpoint

### Search and Filtering
- `GET /api/catalog/search/` - Global search across services and providers
- Query parameters: q, category, location, price_min, price_max, rating_min

## Performance Optimizations

### Database Level
- Proper indexing on frequently queried fields
- Use of select_related() and prefetch_related() for N+1 prevention
- Database-level constraints for data integrity

### API Level
- Pagination for large result sets
- Caching for frequently accessed data
- Optimized serializers for list vs detail views

## Mobile Optimization Features

### Responsive Data
- Mobile-specific description fields
- Optimized image sizes
- Geolocation support for location-based filtering

### Performance
- Minimal data transfer for list views
- Progressive loading for detailed information
- Offline capability considerations

## Next Steps
1. Implement models in Django backend
2. Create database migrations
3. Implement API serializers and viewsets
4. Add comprehensive test coverage
5. Create frontend components for service browsing
