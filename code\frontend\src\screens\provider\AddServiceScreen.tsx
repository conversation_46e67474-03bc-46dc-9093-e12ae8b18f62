import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  BackHandler,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { MultiStepServiceForm } from '../../components/provider/MultiStepServiceForm';
import { ServiceForm } from '../../components/provider/ServiceForm';
import { providerServiceAPI, ServiceCreateData, ServiceCategory } from '../../services/api';

export const AddServiceScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [useMultiStep, setUseMultiStep] = useState(true); // Toggle between multi-step and single form

  useEffect(() => {
    fetchCategories();
  }, []);

  // Handle hardware back button for multi-step form
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Let the MultiStepServiceForm handle the back press if using multi-step
        return useMultiStep ? false : true;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [useMultiStep])
  );

  const fetchCategories = async () => {
    try {
      const response = await providerServiceAPI.getCategories();
      setCategories(response.data || response.results || response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      Alert.alert('Error', 'Failed to load service categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  const handleSubmit = async (data: ServiceCreateData) => {
    setLoading(true);
    try {
      const response = await providerServiceAPI.createService(data);

      Alert.alert(
        'Success!',
        'Your service has been created successfully and is now available to customers.',
        [
          {
            text: 'View Service',
            onPress: () => {
              navigation.navigate('EditService' as never, {
                serviceId: response.data?.id || response.id
              } as never);
            },
          },
          {
            text: 'Create Another',
            onPress: () => {
              // Reset the form by navigating back and forth
              navigation.goBack();
              setTimeout(() => {
                navigation.navigate('AddService' as never);
              }, 100);
            },
          },
          {
            text: 'Done',
            style: 'default',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Failed to create service:', error);

      // Enhanced error handling from EnhancedAddServiceScreen
      if (error.response?.status === 400) {
        const errorData = error.response.data;
        let errorMessage = 'Please check the following errors:\n\n';

        Object.keys(errorData).forEach(field => {
          const fieldErrors = Array.isArray(errorData[field])
            ? errorData[field]
            : [errorData[field]];
          errorMessage += `${field}: ${fieldErrors.join(', ')}\n`;
        });

        Alert.alert('Validation Error', errorMessage);
      } else if (error.response?.status === 403) {
        Alert.alert(
          'Service Limit Reached',
          'You have reached your service limit. Please verify your account or upgrade your plan to add more services.',
          [
            { text: 'OK' },
            { text: 'Upgrade Plan', onPress: () => {
              // Navigate to upgrade screen if available
              console.log('Navigate to upgrade plan');
            }},
          ]
        );
      } else if (error.response?.status === 401) {
        Alert.alert(
          'Authentication Error',
          'Your session has expired. Please log in again.',
          [
            { text: 'OK', onPress: () => {
              // Navigate to login screen
              navigation.reset({
                index: 0,
                routes: [{ name: 'Auth' as never }],
              });
            }},
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to create service. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel',
      'Are you sure you want to cancel? All changes will be lost.',
      [
        { text: 'Continue Editing', style: 'cancel' },
        { text: 'Cancel', style: 'destructive', onPress: () => navigation.goBack() },
      ]
    );
  };

  if (categoriesLoading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleCancel}>
            <Icon name="arrow-back" size={24} color={colors.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Add New Service</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Toggle between multi-step and single form */}
        <View style={styles.toggleContainer}>
          <TouchableOpacity
            style={[styles.toggleButton, useMultiStep && styles.toggleButtonActive]}
            onPress={() => setUseMultiStep(true)}
          >
            <Text style={[styles.toggleText, useMultiStep && styles.toggleTextActive]}>
              Multi-Step
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.toggleButton, !useMultiStep && styles.toggleButtonActive]}
            onPress={() => setUseMultiStep(false)}
          >
            <Text style={[styles.toggleText, !useMultiStep && styles.toggleTextActive]}>
              Single Form
            </Text>
          </TouchableOpacity>
        </View>

        {useMultiStep ? (
          <MultiStepServiceForm
            categories={categories}
            onSubmit={handleSubmit}
            loading={loading}
          />
        ) : (
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <ServiceForm
              categories={categories}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              loading={loading}
              submitButtonText="Create Service"
            />
          </ScrollView>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    ...typography.h2,
    color: colors.textPrimary,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40, // Same width as back button to center the title
  },
  scrollView: {
    flex: 1,
  },
  toggleContainer: {
    flexDirection: 'row',
    marginHorizontal: spacing.lg,
    marginVertical: spacing.md,
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 4,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 6,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: colors.primary,
  },
  toggleText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  toggleTextActive: {
    color: colors.white,
  },
});
