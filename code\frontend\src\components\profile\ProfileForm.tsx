/**
 * ProfileForm Component
 *
 * Editable form for user profile information
 * Following the "digital sanctuary" design philosophy with modern form patterns
 */

import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Switch,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { User, UserProfile } from '../../services/api/profile';
import { PrimaryButton, SecondaryButton } from '../ui';
import { ModernInput } from '../ui/ModernInput';
import { LocationInput } from '../ui/LocationInput';
import { useTheme } from '../../contexts/ThemeContext';
import { Headline2, BodyText, CaptionText } from '../ui/Typography';
import { profileAPI } from '../../services/api/profile';

interface ProfileFormProps {
  user: User;
  profile: UserProfile | null;
  formData: any;
  setFormData: (data: any) => void;
  errors: Record<string, string>;
  onSave: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  enableRealTimeValidation?: boolean;
}

export const ProfileForm: React.FC<ProfileFormProps> = ({
  user,
  profile,
  formData,
  setFormData,
  errors,
  onSave,
  onCancel,
  isLoading = false,
  enableRealTimeValidation = true,
}) => {
  const { colors, borderRadius, shadows, spacing } = useTheme();
  const [realTimeErrors, setRealTimeErrors] = useState<Record<string, string>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [profileCompletion, setProfileCompletion] = useState(0);

  // Calculate profile completion whenever form data changes
  useEffect(() => {
    if (!formData || !user) return;

    const currentUserData = {
      ...user,
      first_name: formData.first_name || user.first_name,
      last_name: formData.last_name || user.last_name,
      email: formData.email || user.email,
      phone: formData.phone || user.phone,
      bio: formData.bio || user.bio,
      date_of_birth: formData.date_of_birth || user.date_of_birth,
    };

    const currentProfileData = {
      ...profile,
      address: formData.address || profile?.address,
      city: formData.city || profile?.city,
      state: formData.state || profile?.state,
      zip_code: formData.zip_code || profile?.zip_code,
      country: formData.country || profile?.country,
      business_name: formData.business_name || profile?.business_name,
      business_description: formData.business_description || profile?.business_description,
      years_of_experience: formData.years_of_experience || profile?.years_of_experience,
      website: formData.website || profile?.website,
      instagram: formData.instagram || profile?.instagram,
      facebook: formData.facebook || profile?.facebook,
    };

    const completion = profileAPI.calculateProfileCompletion(currentUserData, currentProfileData);
    setProfileCompletion(completion);
  }, [formData, user, profile]);

  // Create styles using theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    section: {
      backgroundColor: colors.background.secondary, // Warm Cream background
      marginBottom: spacing.md, // 16pt spacing
      borderRadius: borderRadius.lg, // 12pt corner radius as per design system
      padding: spacing.md, // 16pt internal padding
      ...shadows.level1, // Level 1 elevation shadow for cards
    },
    completionSection: {
      backgroundColor: colors.background.primary,
      marginBottom: spacing.md,
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      ...shadows.level1,
      borderWidth: 1,
      borderColor: colors.primary.main,
    },
    completionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    completionText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text.primary,
    },
    completionPercentage: {
      fontSize: 16,
      fontWeight: 'bold',
      color: colors.primary.main,
    },
    completionBar: {
      height: 8,
      backgroundColor: colors.background.secondary,
      borderRadius: 4,
      overflow: 'hidden',
    },
    completionProgress: {
      height: '100%',
      backgroundColor: colors.primary.main,
      borderRadius: 4,
    },
    completionHint: {
      fontSize: 12,
      color: colors.text.secondary,
      marginTop: spacing.xs,
      fontStyle: 'italic',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.lg,
      gap: spacing.md,
    },
    button: {
      flex: 1,
    },
    switchContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.xs,
      marginBottom: spacing.xs,
      backgroundColor: colors.background.primary,
      borderRadius: borderRadius.md,
      borderWidth: 1,
      borderColor: colors.extended.sageVariant,
    },
    switchLabel: {
      flex: 1,
      fontSize: 14,
      color: colors.text.primary,
      marginRight: spacing.sm,
    },
  });

  // Enhanced real-time validation function using comprehensive API validation
  const validateField = useCallback((field: string, value: any) => {
    if (!enableRealTimeValidation || !formData) return null;

    // Create validation data objects for different validation types
    const userValidationData = {
      first_name: formData.first_name,
      last_name: formData.last_name,
      email: formData.email,
      phone: formData.phone,
      bio: formData.bio,
      date_of_birth: formData.date_of_birth,
      [field]: value,
    };

    const businessValidationData = {
      website: formData.website,
      years_of_experience: formData.years_of_experience,
      instagram: formData.instagram,
      facebook: formData.facebook,
      business_description: formData.business_description,
      [field]: value,
    };

    const locationValidationData = {
      latitude: formData.latitude,
      longitude: formData.longitude,
      search_radius: formData.search_radius,
      [field]: value,
    };

    // Use appropriate validation based on field type
    let validationResult;

    if (['first_name', 'last_name', 'email', 'phone', 'bio', 'date_of_birth'].includes(field)) {
      validationResult = profileAPI.validateProfileData(userValidationData);
    } else if (['website', 'years_of_experience', 'instagram', 'facebook', 'business_description'].includes(field)) {
      validationResult = profileAPI.validateBusinessProfileData(businessValidationData);
    } else if (['latitude', 'longitude', 'search_radius'].includes(field)) {
      validationResult = profileAPI.validateLocationData(locationValidationData);
    } else {
      // For other fields, use basic validation
      validationResult = { isValid: true, errors: [] };
    }

    // Return specific error for this field if validation failed
    if (!validationResult.isValid && validationResult.errors.length > 0) {
      // Find the most relevant error for this field
      const fieldError = validationResult.errors.find(error =>
        error.toLowerCase().includes(field.replace('_', ' ').toLowerCase())
      );
      return fieldError || validationResult.errors[0];
    }

    // Additional field-specific validation for required fields
    switch (field) {
      case 'first_name':
        return !value?.trim() ? 'First name is required' : null;
      case 'last_name':
        return !value?.trim() ? 'Last name is required' : null;
      case 'email':
        return !value?.trim() ? 'Email is required' : null;
      case 'business_name':
        return user?.role === 'service_provider' && !value?.trim() ? 'Business name is required for service providers' : null;
      default:
        return null;
    }
  }, [enableRealTimeValidation, formData, user?.role]);

  const updateFormData = useCallback((field: string, value: string | number) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));

    setHasUnsavedChanges(true);

    // Real-time validation
    if (enableRealTimeValidation) {
      const error = validateField(field, value);
      setRealTimeErrors(prev => ({
        ...prev,
        [field]: error || '',
      }));
    }
  }, [validateField, enableRealTimeValidation, setFormData]);

  const handleSave = useCallback(() => {
    // Perform comprehensive validation before saving
    const validationResult = validateCompleteForm();

    if (!validationResult.isValid) {
      // Set validation errors for display
      const newErrors: Record<string, string> = {};
      validationResult.errors.forEach((error, index) => {
        newErrors[`validation_${index}`] = error;
      });
      setRealTimeErrors(prev => ({ ...prev, ...newErrors }));

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setHasUnsavedChanges(false);
    setRealTimeErrors({}); // Clear any validation errors
    onSave();
  }, [onSave, validateCompleteForm]);

  const handleCancel = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setHasUnsavedChanges(false);
    setRealTimeErrors({});
    onCancel();
  }, [onCancel]);

  // Enhanced form validation check
  const isFormValid = useCallback(() => {
    if (!formData) return false;

    const hasErrors = Object.values(errors).some(error => error) ||
                     Object.values(realTimeErrors).some(error => error);

    // Required fields validation
    const hasRequiredFields = formData.first_name?.trim() && formData.last_name?.trim();

    // Additional validation for service providers
    const hasBusinessRequiredFields = user?.role !== 'service_provider' ||
                                     formData.business_name?.trim();

    return !hasErrors && hasRequiredFields && hasBusinessRequiredFields;
  }, [errors, realTimeErrors, formData, user?.role]);

  // Enhanced comprehensive validation for the entire form
  const validateCompleteForm = useCallback(() => {
    const userValidationData = {
      first_name: formData.first_name || user.first_name,
      last_name: formData.last_name || user.last_name,
      email: formData.email || user.email,
      phone: formData.phone || user.phone,
      bio: formData.bio || user.bio,
      date_of_birth: formData.date_of_birth || user.date_of_birth,
    };

    const profileValidationData = {
      address: formData.address || profile?.address,
      city: formData.city || profile?.city,
      state: formData.state || profile?.state,
      zip_code: formData.zip_code || profile?.zip_code,
      country: formData.country || profile?.country,
      business_name: formData.business_name || profile?.business_name,
      business_description: formData.business_description || profile?.business_description,
      years_of_experience: formData.years_of_experience || profile?.years_of_experience,
      website: formData.website || profile?.website,
      instagram: formData.instagram || profile?.instagram,
      facebook: formData.facebook || profile?.facebook,
    };

    return profileAPI.validateCompleteProfile(userValidationData, profileValidationData);
  }, [formData, user, profile]);

  const FormSection: React.FC<{
    title: string;
    children: React.ReactNode;
  }> = ({ title, children }) => (
    <View style={styles.section}>
      <Headline2 color="secondary" style={{ marginBottom: spacing.md }}>
        {title}
      </Headline2>
      {children}
    </View>
  );

  // Profile completion indicator component
  const ProfileCompletionIndicator = () => (
    <View style={styles.completionSection}>
      <View style={styles.completionHeader}>
        <Headline2 color="primary" style={{ fontSize: 16, fontWeight: '600' }}>
          Profile Completion
        </Headline2>
        <View style={styles.completionPercentage}>
          <Headline2 color="primary" style={{ fontSize: 16, fontWeight: 'bold' }}>
            {profileCompletion}%
          </Headline2>
        </View>
      </View>
      <View style={styles.completionBar}>
        <View style={[styles.completionProgress, { width: `${profileCompletion}%` }]} />
      </View>
      {profileCompletion < 100 && (
        <View style={styles.completionHint}>
          <Headline2 color="secondary" style={{ fontSize: 12, fontStyle: 'italic' }}>
            {profileCompletion < 50
              ? "Complete your basic information to improve your profile"
              : profileCompletion < 80
              ? "Add business details to enhance your professional presence"
              : "You're almost done! Fill in the remaining fields"}
          </Headline2>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Completion Indicator */}
        <ProfileCompletionIndicator />

        {/* Personal Information */}
        <FormSection title="Personal Information">
          <ModernInput
            label="First Name"
            value={formData?.first_name || user?.first_name || ''}
            onChangeText={(value) => updateFormData('first_name', value)}
            error={errors.first_name || realTimeErrors.first_name}
            testID="first-name-input"
            required
          />

          <ModernInput
            label="Last Name"
            value={formData?.last_name || user?.last_name || ''}
            onChangeText={(value) => updateFormData('last_name', value)}
            error={errors.last_name || realTimeErrors.last_name}
            testID="last-name-input"
            required
          />

          <ModernInput
            label="Email Address"
            value={formData?.email || user?.email || ''}
            onChangeText={(value) => updateFormData('email', value)}
            error={errors.email || realTimeErrors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            testID="email-input"
            required
          />

          <ModernInput
            label="Phone Number"
            value={formData?.phone || user?.phone || ''}
            onChangeText={(value) => updateFormData('phone', value)}
            error={errors.phone || realTimeErrors.phone}
            keyboardType="phone-pad"
            testID="phone-input"
            helperText="Include country code (e.g., +1234567890)"
          />

          <ModernInput
            label="Date of Birth"
            value={formData?.date_of_birth || user?.date_of_birth || ''}
            onChangeText={(value) => updateFormData('date_of_birth', value)}
            error={errors.date_of_birth || realTimeErrors.date_of_birth}
            helperText="Format: YYYY-MM-DD"
            testID="date-of-birth-input"
          />

          <ModernInput
            label="Bio"
            value={formData?.bio || user?.bio || ''}
            onChangeText={(value) => updateFormData('bio', value)}
            error={errors.bio || realTimeErrors.bio}
            helperText="Tell us about yourself (max 500 characters)"
            multiline
            numberOfLines={3}
            testID="bio-input"
            maxLength={500}
          />
        </FormSection>

        {/* Address Information */}
        <FormSection title="Location">
          <LocationInput
            label="Street Address"
            value={formData?.address || profile?.address || ''}
            onChangeText={(value) => updateFormData('address', value)}
            onLocationSelect={(location) => {
              updateFormData('address', location.address);
              updateFormData('city', location.city);
              updateFormData('state', location.state);
              updateFormData('country', location.country);
              if (location.latitude && location.longitude) {
                updateFormData('latitude', location.latitude);
                updateFormData('longitude', location.longitude);
              }
            }}
            error={errors.address}
            helperText="Start typing to search for addresses or use current location"
            testID="address-input"
          />

          <ModernInput
            label="City"
            value={formData?.city || profile?.city || ''}
            onChangeText={(value) => updateFormData('city', value)}
            error={errors.city}
            testID="city-input"
          />

          <ModernInput
            label="State/Province"
            value={formData?.state || profile?.state || ''}
            onChangeText={(value) => updateFormData('state', value)}
            error={errors.state}
            testID="state-input"
          />

          <ModernInput
            label="Postal Code"
            value={formData?.postal_code || profile?.postal_code || ''}
            onChangeText={(value) => updateFormData('postal_code', value)}
            error={errors.postal_code}
            testID="postal-code-input"
          />

          <ModernInput
            label="Country"
            value={formData?.country || profile?.country || ''}
            onChangeText={(value) => updateFormData('country', value)}
            error={errors.country}
            testID="country-input"
          />
        </FormSection>

        {/* Professional Information (for providers) */}
        {user?.role === 'service_provider' && (
          <FormSection title="Business Information">
            <ModernInput
              label="Business Name"
              value={formData?.business_name || profile?.business_name || ''}
              onChangeText={(value) => updateFormData('business_name', value)}
              error={errors.business_name}
              testID="business-name-input"
              required
            />

            <ModernInput
              label="Business Description"
              value={formData?.business_description || profile?.business_description || ''}
              onChangeText={(value) => updateFormData('business_description', value)}
              error={errors.business_description || realTimeErrors.business_description}
              helperText={`Describe the services you provide (${(formData?.business_description || profile?.business_description || '').length}/1000 characters)`}
              multiline
              numberOfLines={3}
              testID="business-description-input"
            />

            <ModernInput
              label="Years of Experience"
              value={formData?.years_of_experience?.toString() || profile?.years_of_experience?.toString() || ''}
              onChangeText={(value) => updateFormData('years_of_experience', parseInt(value) || 0)}
              error={errors.years_of_experience || realTimeErrors.years_of_experience}
              keyboardType="numeric"
              helperText="Enter your years of professional experience (0-50)"
              testID="years-experience-input"
            />

            <ModernInput
              label="Website"
              value={formData?.website || profile?.website || ''}
              onChangeText={(value) => updateFormData('website', value)}
              error={errors.website || realTimeErrors.website}
              helperText="Your business website URL (e.g., https://example.com)"
              keyboardType="url"
              autoCapitalize="none"
              testID="website-input"
            />

            <ModernInput
              label="Instagram"
              value={formData?.instagram || profile?.instagram || ''}
              onChangeText={(value) => updateFormData('instagram', value)}
              error={errors.instagram || realTimeErrors.instagram}
              helperText="Your Instagram profile URL (e.g., https://instagram.com/yourhandle)"
              autoCapitalize="none"
              testID="instagram-input"
            />

            <ModernInput
              label="Facebook"
              value={formData?.facebook || profile?.facebook || ''}
              onChangeText={(value) => updateFormData('facebook', value)}
              error={errors.facebook || realTimeErrors.facebook}
              helperText="Your Facebook page URL (e.g., https://facebook.com/yourpage)"
              autoCapitalize="none"
              testID="facebook-input"
            />
          </FormSection>
        )}

        {/* Preferences Section */}
        <FormSection title="Preferences & Privacy">
          <ModernInput
            label="Search Radius (miles)"
            value={formData?.search_radius?.toString() || profile?.search_radius?.toString() || '25'}
            onChangeText={(value) => updateFormData('search_radius', parseInt(value) || 25)}
            error={errors.search_radius}
            keyboardType="numeric"
            helperText="How far you're willing to travel for services (1-100 miles)"
            testID="search-radius-input"
          />

          {/* Privacy Settings */}
          <View style={{ marginTop: spacing.md }}>
            <CaptionText style={{ marginBottom: spacing.sm, color: colors.text.secondary }}>
              Privacy Settings
            </CaptionText>

            <View style={styles.switchContainer}>
              <BodyText style={styles.switchLabel}>Show phone number publicly</BodyText>
              <Switch
                value={formData?.show_phone_publicly ?? profile?.show_phone_publicly ?? false}
                onValueChange={(value) => updateFormData('show_phone_publicly', value)}
                trackColor={{ false: colors.extended.sageVariant, true: colors.primary }}
                thumbColor={colors.background.primary}
                testID="show-phone-switch"
              />
            </View>

            <View style={styles.switchContainer}>
              <BodyText style={styles.switchLabel}>Show email address publicly</BodyText>
              <Switch
                value={formData?.show_email_publicly ?? profile?.show_email_publicly ?? false}
                onValueChange={(value) => updateFormData('show_email_publicly', value)}
                trackColor={{ false: colors.extended.sageVariant, true: colors.primary }}
                thumbColor={colors.background.primary}
                testID="show-email-switch"
              />
            </View>

            <View style={styles.switchContainer}>
              <BodyText style={styles.switchLabel}>Allow reviews and ratings</BodyText>
              <Switch
                value={formData?.allow_reviews ?? profile?.allow_reviews ?? true}
                onValueChange={(value) => updateFormData('allow_reviews', value)}
                trackColor={{ false: colors.extended.sageVariant, true: colors.primary }}
                thumbColor={colors.background.primary}
                testID="allow-reviews-switch"
              />
            </View>

            {/* Service Provider Specific Preferences */}
            {user?.role === 'service_provider' && (
              <View style={styles.switchContainer}>
                <BodyText style={styles.switchLabel}>Auto-accept booking requests</BodyText>
                <Switch
                  value={formData?.auto_accept_bookings ?? profile?.auto_accept_bookings ?? false}
                  onValueChange={(value) => updateFormData('auto_accept_bookings', value)}
                  trackColor={{ false: colors.extended.sageVariant, true: colors.primary }}
                  thumbColor={colors.background.primary}
                  testID="auto-accept-switch"
                />
              </View>
            )}
          </View>
        </FormSection>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <SecondaryButton
            onPress={handleCancel}
            disabled={isLoading}
            testID="cancel-profile-button"
            style={styles.button}
          >
            Cancel
          </SecondaryButton>

          <PrimaryButton
            onPress={handleSave}
            disabled={isLoading || !isFormValid()}
            loading={isLoading}
            testID="save-profile-button"
            style={styles.button}
          >
            {isLoading
              ? "Saving..."
              : hasUnsavedChanges
                ? `Save Changes (${profileCompletion}% Complete)`
                : profileCompletion === 100
                  ? "Profile Complete ✓"
                  : "No Changes"}
          </PrimaryButton>
        </View>
      </ScrollView>
    </View>
  );
};

// Styles are now defined inline using theme

export default ProfileForm;
