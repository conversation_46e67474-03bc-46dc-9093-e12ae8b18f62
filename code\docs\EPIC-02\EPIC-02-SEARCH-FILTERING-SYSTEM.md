# EPIC-02: Service Search & Filtering System Design

## Overview
This document outlines the comprehensive search and filtering system for service discovery, ensuring 100% parity with the legacy system while optimizing for mobile-first user experience.

## Backend Search & Filtering Architecture

### Django Filter Integration
Building on the legacy system's comprehensive filtering capabilities:

#### ServiceFilter Class
```python
class ServiceFilter(django_filters.FilterSet):
    # Text search
    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')
    search = django_filters.CharFilter(method='filter_search')
    
    # Provider filters
    provider_name = django_filters.CharFilter(field_name='provider__business_name', lookup_expr='icontains')
    provider_city = django_filters.CharFilter(field_name='provider__city', lookup_expr='icontains')
    provider_verified = django_filters.BooleanFilter(field_name='provider__is_verified')
    min_rating = django_filters.NumberFilter(field_name='provider__rating', lookup_expr='gte')
    
    # Category filters
    category = django_filters.ModelChoiceFilter(queryset=ServiceCategory.objects.filter(is_active=True))
    categories = django_filters.ModelMultipleChoiceFilter(
        field_name='category',
        queryset=ServiceCategory.objects.filter(is_active=True),
        conjoined=False  # OR logic
    )
    
    # Price filters
    min_price = django_filters.NumberFilter(field_name='base_price', lookup_expr='gte')
    max_price = django_filters.NumberFilter(field_name='base_price', lookup_expr='lte')
    price_type = django_filters.ChoiceFilter(choices=Service.PRICE_TYPE_CHOICES)
    
    # Duration filters
    min_duration = django_filters.NumberFilter(field_name='duration', lookup_expr='gte')
    max_duration = django_filters.NumberFilter(field_name='duration', lookup_expr='lte')
    
    # Status filters
    is_popular = django_filters.BooleanFilter()
    is_available = django_filters.BooleanFilter()
    has_location = django_filters.BooleanFilter(method='filter_has_location')
    
    # Sorting
    sort_by = django_filters.CharFilter(method='filter_sort')
```

#### Advanced Search Method
```python
def filter_search(self, queryset, name, value):
    """Multi-field search with relevance scoring"""
    if value:
        return queryset.filter(
            Q(name__icontains=value) |
            Q(description__icontains=value) |
            Q(provider__business_name__icontains=value) |
            Q(category__name__icontains=value) |
            Q(provider__city__icontains=value)
        ).distinct()
    return queryset
```

#### Sorting Options
```python
SORT_OPTIONS = {
    'relevance': ['-is_popular', 'base_price', 'name'],
    'price_low': ['base_price'],
    'price_high': ['-base_price'],
    'rating': ['-provider__rating'],
    'popularity': ['-is_popular', '-booking_count'],
    'duration': ['duration'],
    'newest': ['-created_at'],
    'distance': ['distance']  # Calculated in view
}
```

## Frontend Search Interface

### Search State Management
```typescript
interface SearchState {
  query: string;
  filters: SearchFilters;
  results: {
    services: Service[];
    providers: ServiceProvider[];
    totalCount: number;
  };
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  sortBy: SortOption;
}

interface SearchFilters {
  categories: string[];
  priceRange: {
    min: number | null;
    max: number | null;
  };
  durationRange: {
    min: number | null;
    max: number | null;
  };
  rating: number | null;
  location: {
    latitude: number;
    longitude: number;
    radius: number; // km
  } | null;
  isVerified: boolean | null;
  isPopular: boolean | null;
  isAvailable: boolean | null;
  priceType: 'fixed' | 'hourly' | 'range' | 'consultation' | null;
}
```

### Search Hook Implementation
```typescript
export const useServiceSearch = () => {
  const [searchState, setSearchState] = useState<SearchState>(initialState);
  
  const searchServices = useCallback(async (query: string, filters?: SearchFilters) => {
    setSearchState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const params = {
        search: query,
        ...filters,
        page: 1,
        limit: 20,
      };
      
      const response = await serviceApi.searchServices(params);
      
      setSearchState(prev => ({
        ...prev,
        query,
        filters: filters || prev.filters,
        results: response.data,
        hasMore: response.hasMore,
        page: 1,
        isLoading: false,
      }));
    } catch (error) {
      setSearchState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false,
      }));
    }
  }, []);
  
  const loadMore = useCallback(async () => {
    if (!searchState.hasMore || searchState.isLoading) return;
    
    // Implementation for pagination
  }, [searchState]);
  
  return {
    ...searchState,
    searchServices,
    loadMore,
    clearSearch: () => setSearchState(initialState),
  };
};
```

## Search UI Components

### SearchBar Component
```typescript
interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSubmit: () => void;
  onFilterPress: () => void;
  placeholder?: string;
  showFilter?: boolean;
  filterCount?: number;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChangeText,
  onSubmit,
  onFilterPress,
  placeholder = "Search services...",
  showFilter = true,
  filterCount = 0,
}) => {
  return (
    <SearchContainer>
      <SearchInput
        value={value}
        onChangeText={onChangeText}
        onSubmitEditing={onSubmit}
        placeholder={placeholder}
        returnKeyType="search"
      />
      <SearchIcon name="search" size={20} />
      {showFilter && (
        <FilterButton onPress={onFilterPress}>
          <FilterIcon name="filter" size={20} />
          {filterCount > 0 && (
            <FilterBadge>
              <FilterBadgeText>{filterCount}</FilterBadgeText>
            </FilterBadge>
          )}
        </FilterButton>
      )}
    </SearchContainer>
  );
};
```

### FilterPanel Component
```typescript
interface FilterPanelProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onApply: () => void;
  onClear: () => void;
  categories: ServiceCategory[];
  visible: boolean;
  onClose: () => void;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFiltersChange,
  onApply,
  onClear,
  categories,
  visible,
  onClose,
}) => {
  return (
    <Modal visible={visible} animationType="slide">
      <FilterContainer>
        <FilterHeader>
          <FilterTitle>Filters</FilterTitle>
          <CloseButton onPress={onClose}>
            <Icon name="close" size={24} />
          </CloseButton>
        </FilterHeader>
        
        <ScrollView>
          {/* Categories Section */}
          <FilterSection>
            <SectionTitle>Categories</SectionTitle>
            <CategoryGrid>
              {categories.map(category => (
                <CategoryChip
                  key={category.id}
                  category={category}
                  selected={filters.categories.includes(category.id)}
                  onToggle={(id) => toggleCategory(id)}
                />
              ))}
            </CategoryGrid>
          </FilterSection>
          
          {/* Price Range Section */}
          <FilterSection>
            <SectionTitle>Price Range</SectionTitle>
            <PriceRangeSlider
              min={0}
              max={500}
              values={[filters.priceRange.min || 0, filters.priceRange.max || 500]}
              onValuesChange={(values) => updatePriceRange(values)}
            />
          </FilterSection>
          
          {/* Duration Section */}
          <FilterSection>
            <SectionTitle>Duration</SectionTitle>
            <DurationOptions>
              {DURATION_OPTIONS.map(option => (
                <DurationChip
                  key={option.value}
                  option={option}
                  selected={isDurationSelected(option)}
                  onPress={() => toggleDuration(option)}
                />
              ))}
            </DurationOptions>
          </FilterSection>
          
          {/* Rating Section */}
          <FilterSection>
            <SectionTitle>Minimum Rating</SectionTitle>
            <RatingSelector
              value={filters.rating}
              onSelect={(rating) => updateRating(rating)}
            />
          </FilterSection>
          
          {/* Additional Filters */}
          <FilterSection>
            <SectionTitle>Additional Options</SectionTitle>
            <CheckboxOption
              label="Verified Providers Only"
              checked={filters.isVerified}
              onToggle={(value) => updateFilter('isVerified', value)}
            />
            <CheckboxOption
              label="Popular Services"
              checked={filters.isPopular}
              onToggle={(value) => updateFilter('isPopular', value)}
            />
            <CheckboxOption
              label="Available Now"
              checked={filters.isAvailable}
              onToggle={(value) => updateFilter('isAvailable', value)}
            />
          </FilterSection>
        </ScrollView>
        
        <FilterActions>
          <ClearButton onPress={onClear}>
            <ButtonText>Clear All</ButtonText>
          </ClearButton>
          <ApplyButton onPress={onApply}>
            <ButtonText>Apply Filters</ButtonText>
          </ApplyButton>
        </FilterActions>
      </FilterContainer>
    </Modal>
  );
};
```

### SortSelector Component
```typescript
interface SortSelectorProps {
  value: SortOption;
  onSelect: (option: SortOption) => void;
  options: SortOption[];
}

export const SortSelector: React.FC<SortSelectorProps> = ({
  value,
  onSelect,
  options,
}) => {
  return (
    <SortContainer>
      <SortLabel>Sort by:</SortLabel>
      <SortDropdown
        value={value}
        onValueChange={onSelect}
        items={options.map(option => ({
          label: option.label,
          value: option.value,
        }))}
      />
    </SortContainer>
  );
};
```

## Search Performance Optimizations

### Debounced Search
```typescript
const useDebouncedSearch = (searchFunction: Function, delay: number = 300) => {
  const [debouncedValue, setDebouncedValue] = useState('');
  
  useEffect(() => {
    const handler = setTimeout(() => {
      if (debouncedValue.trim()) {
        searchFunction(debouncedValue);
      }
    }, delay);
    
    return () => clearTimeout(handler);
  }, [debouncedValue, delay, searchFunction]);
  
  return setDebouncedValue;
};
```

### Search Result Caching
```typescript
const searchCache = new Map<string, SearchResponse>();

const getCacheKey = (query: string, filters: SearchFilters): string => {
  return JSON.stringify({ query, filters });
};

const getCachedResults = (query: string, filters: SearchFilters): SearchResponse | null => {
  const key = getCacheKey(query, filters);
  return searchCache.get(key) || null;
};

const setCachedResults = (query: string, filters: SearchFilters, results: SearchResponse): void => {
  const key = getCacheKey(query, filters);
  searchCache.set(key, results);
  
  // Limit cache size
  if (searchCache.size > 50) {
    const firstKey = searchCache.keys().next().value;
    searchCache.delete(firstKey);
  }
};
```

## Search Analytics & Insights

### Search Tracking
```typescript
interface SearchAnalytics {
  query: string;
  filters: SearchFilters;
  resultsCount: number;
  timestamp: Date;
  userId?: string;
  selectedResult?: string;
}

const trackSearch = (analytics: SearchAnalytics) => {
  // Track search queries for insights
  analyticsService.track('service_search', analytics);
};
```

## API Endpoints

### Search Endpoints
```
GET /api/catalog/search/services/
  - Query parameters: search, category, min_price, max_price, min_rating, etc.
  - Response: Paginated service results with metadata

GET /api/catalog/search/suggestions/
  - Query parameters: q (partial query)
  - Response: Search suggestions and autocomplete

GET /api/catalog/categories/
  - Response: Available categories for filtering

GET /api/catalog/search/popular/
  - Response: Popular search terms and trending services
```

## Mobile-Specific Features

### Location-Based Search
- Automatic location detection
- Distance-based sorting
- Radius filtering
- Map view integration

### Voice Search
- Speech-to-text integration
- Voice command processing
- Accessibility support

### Offline Search
- Cached recent searches
- Offline search history
- Progressive enhancement

## Testing Strategy

### Backend Testing
- Filter functionality tests
- Search relevance tests
- Performance benchmarks
- Edge case handling

### Frontend Testing
- Component interaction tests
- Search flow testing
- Filter state management
- Performance testing

## Implementation Priority

### Phase 1: Core Search
1. Basic text search
2. Category filtering
3. Price range filtering
4. Sort functionality

### Phase 2: Advanced Filters
1. Rating filters
2. Duration filters
3. Location-based search
4. Provider verification filters

### Phase 3: Enhanced Features
1. Search suggestions
2. Voice search
3. Search analytics
4. Advanced caching

## Next Steps
1. Implement backend ServiceFilter class
2. Create search API endpoints
3. Build SearchBar component
4. Implement FilterPanel component
5. Add search state management
6. Integrate with service listing screens
