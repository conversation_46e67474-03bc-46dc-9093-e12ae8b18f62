/**
 * Authentication Context
 * Manages user authentication state and provides auth methods
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI, AuthResponse } from '../services/api/auth';

export type UserRole = 'customer' | 'service_provider' | 'admin';

export interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  role: UserRole;
  is_verified: boolean;
  account_status: string;
  avatar?: string;
  phone?: string;
  created_at: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  isCustomer: boolean;
  isProvider: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const accessToken = await AsyncStorage.getItem('access_token');
      const userData = await AsyncStorage.getItem('user');

      if (accessToken && userData) {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        
        // Optionally verify token is still valid
        try {
          const currentUser = await authAPI.checkAuthStatus();
          setUser(currentUser);
        } catch (error) {
          // Token might be expired, clear stored data
          await clearAuthData();
        }
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      await clearAuthData();
    } finally {
      setIsLoading(false);
    }
  };

  const clearAuthData = async () => {
    await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'user']);
    setUser(null);
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log('[AuthContext] Starting login process for:', email);
      setIsLoading(true);
      const response: AuthResponse = await authAPI.login({ email, password });

      console.log('[AuthContext] Login API call successful, storing tokens...');
      // Store tokens and user data
      await AsyncStorage.multiSet([
        ['access_token', response.access],
        ['refresh_token', response.refresh],
        ['user', JSON.stringify(response.user)],
      ]);

      console.log('[AuthContext] Tokens stored, setting user state...');
      setUser(response.user);
      console.log('[AuthContext] Login process completed successfully');
      return true;
    } catch (error) {
      console.error('[AuthContext] Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      const refreshToken = await AsyncStorage.getItem('refresh_token');
      
      if (refreshToken) {
        try {
          await authAPI.logout(refreshToken);
        } catch (error) {
          // Ignore logout API errors, still clear local data
          console.warn('Logout API error:', error);
        }
      }

      await clearAuthData();
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local data even if there's an error
      await clearAuthData();
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const currentUser = await authAPI.getProfile();
      setUser(currentUser);
      await AsyncStorage.setItem('user', JSON.stringify(currentUser));
    } catch (error) {
      console.error('Error refreshing user:', error);
      // If refresh fails, user might need to re-authenticate
      await clearAuthData();
    }
  };

  const isAuthenticated = !!user;
  const isCustomer = user?.role === 'customer';
  const isProvider = user?.role === 'service_provider';
  const isAdmin = user?.role === 'admin';

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshUser,
    isCustomer,
    isProvider,
    isAdmin,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
