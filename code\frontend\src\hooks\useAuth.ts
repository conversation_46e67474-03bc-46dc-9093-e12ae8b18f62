/**
 * Authentication Hook
 * Comprehensive authentication hook with state management and error handling
 * Consolidated from useEnhancedAuth.ts per Rule R-003
 */

import { useState, useEffect, useCallback } from 'react';
import { authService, AuthState, LoginResult } from '../services/authService';
import { LoginRequest } from '../services/api/auth';

export interface UseAuthReturn {
  // State
  isAuthenticated: boolean;
  user: AuthState['user'];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<LoginResult>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  
  // Utilities
  hasRole: (role: 'customer' | 'service_provider' | 'admin') => boolean;
  isCustomer: boolean;
  isProvider: boolean;
  isAdmin: boolean;
}

/**
 * Comprehensive authentication hook
 * Provides complete authentication state management and utilities
 */
export const useAuth = (): UseAuthReturn => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
  });
  const [error, setError] = useState<string | null>(null);

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      const currentState = await authService.getCurrentAuthState();
      setAuthState(currentState);
    } catch (err) {
      console.error('Failed to initialize auth:', err);
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
      });
    }
  }, []);

  const login = useCallback(async (credentials: LoginRequest): Promise<LoginResult> => {
    try {
      setError(null);
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      const result = await authService.login(credentials);
      
      if (result.success) {
        const newState = await authService.getCurrentAuthState();
        setAuthState(newState);
      } else {
        setError(result.error || 'Login failed');
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
      
      return result;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(errorMessage);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      setError(null);
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      await authService.logout();
      
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
      });
    } catch (err: any) {
      console.error('Logout error:', err);
      setError(err.message || 'Logout failed');
      
      // Force logout on error
      setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false,
      });
    }
  }, []);

  const refreshUser = useCallback(async (): Promise<void> => {
    try {
      setError(null);
      const currentState = await authService.getCurrentAuthState();
      setAuthState(currentState);
    } catch (err: any) {
      console.error('Failed to refresh user:', err);
      setError(err.message || 'Failed to refresh user data');
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const hasRole = useCallback((role: 'customer' | 'service_provider' | 'admin'): boolean => {
    return authState.user?.role === role;
  }, [authState.user]);

  const isCustomer = authState.user?.role === 'customer';
  const isProvider = authState.user?.role === 'service_provider';
  const isAdmin = authState.user?.role === 'admin';

  return {
    // State
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    isLoading: authState.isLoading,
    error,
    
    // Actions
    login,
    logout,
    refreshUser,
    clearError,
    
    // Utilities
    hasRole,
    isCustomer,
    isProvider,
    isAdmin,
  };
};

// Export default for backward compatibility
export default useAuth;
